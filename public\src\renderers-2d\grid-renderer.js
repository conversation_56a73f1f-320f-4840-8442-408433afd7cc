// @ts-check

import Renderer2D from "../utils/renderer-2d.js";
import Vector2Builder from "../utils/vector.js";

// @ts-check

const Vector2Int32 = Vector2Builder.from(Int32Array);
const Vector2 = Vector2Builder.from(Array);

export default class GridRenderer extends Renderer2D {
  #cellWidth;
  #cellHeight;
  #rows;
  #columns;
  #mousePosition = new Vector2Int32;
  #cellHoverPosition = new Vector2;
  #renderConfig = {
    grid: true,
    hoverPosition: true
  };

  /**
   * @param {number} [rows]
   * @param {number} [columns]
   * @param {number} [cellWidth]
   * @param {number} [cellHeight]
   * @param {import("../utils/renderer-2d.js").Renderer2DConfig} [config]
  */
  constructor(rows, columns, cellWidth, cellHeight, config) {
    super(config);
    this.#rows = rows ?? this.#getIntAttributeValue("rows") ?? 1;
    this.#columns = columns ?? this.#getIntAttributeValue("columns") ?? 1;
    this.#cellWidth = cellWidth ?? this.#getIntAttributeValue("cell-width");
    this.#cellHeight = cellHeight ?? this.#getIntAttributeValue("cell-height");
    this.renderGrid();
  }

  get rows() {
    return this.#rows;
  }
  get columns() {
    return this.#columns;
  }

  get cellWidth() {
    if(this.#cellWidth === undefined) {
      return Math.round(this.width / this.#columns);
    }
    return this.#cellWidth;
  }
  get cellHeight() {
    if(this.#cellHeight === undefined) {
      return Math.round(this.height / this.#rows);
    }
    return this.#cellHeight;
  }

  get gridWidth() {
    if(this.#cellWidth === undefined) {
      return this.width;
    }
    return this.#cellWidth * this.#columns;
  }
  get gridHeight() {
    if(this.#cellHeight === undefined) {
      return this.height;
    }
    return this.#cellHeight * this.#rows;
  }

  /** @param {boolean} value */
  set gridVisible(value) {
    this.#renderConfig.grid = value;
  }

  /** @param {boolean} value */
  set hoverPositionVisible(value) {
    this.#renderConfig.hoverPosition = value;
  }

  connectedCallback() {
    window.addEventListener("pointermove", event => {
      this.#mousePosition.set(event.x - this.offsetLeft, event.y - this.offsetTop);
      const column = Math.floor(this.#mousePosition[0] / this.gridWidth * this.#columns);
      const row = Math.floor(this.#mousePosition[1] / this.gridHeight * this.#rows);
      this.#cellHoverPosition.set(column, row);
    });
  }

  #getIntAttributeValue(name) {
    const attributeValue = this.getAttribute(name);
    if(attributeValue === null) {
      return;
    }
    const parsedValue = Number.parseInt(attributeValue);
    if(Number.isNaN(parsedValue)) {
      return;
    }
    return parsedValue;
  }

  clear() {
    super.clear();
  }

  renderGrid() {
    const cellWidth = this.cellWidth;
    const cellHeight = this.cellHeight;
    const gridWidth = this.gridWidth;
    const gridHeight = this.gridHeight;
    for(let r = 1; r < this.#rows; r++) {
      const y = r * cellHeight;
      this.line(0, y, gridWidth, y, { stroke: "#fff" });
    }
    for(let c = 1; c < this.#columns; c++) {
      const x = c * cellWidth;
      this.line(x, 0, x, gridHeight, { stroke: "#fff" });
    }
  }

  renderCellHover() {
    const cellWidth = this.cellWidth;
    const cellHeight = this.cellHeight;
    const x = this.#cellHoverPosition[0] * cellWidth;
    const y = this.#cellHoverPosition[1] * cellHeight;
    this.rect(x, y, cellWidth, cellHeight, { fill: "#fbf6" });
  }

  render() {
    this.clear();
    if(this.#renderConfig.grid) {
      this.renderGrid();
    }
    if(this.#renderConfig.hoverPosition) {
      this.renderCellHover();
    }
  }

  #intervalId;

  /** @param {number} fps */
  run(fps = 30) {
    clearInterval(this.#intervalId);
    const timeMs = Math.floor(1000 / fps);
    this.#intervalId = setInterval(instance => instance.render(), timeMs, this);
  }

  stop() {
    clearTimeout(this.#intervalId);
  }

  /** @type {Readonly<[ tagName: string, constructor: typeof HTMLElement, options: ElementDefinitionOptions ]>} */
  static definition = Object.freeze([ "grid-renderer", this, { extends: "canvas" } ]);

}
