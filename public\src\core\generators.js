/**
 * @param {"JavaScript"} language
 * @param {Record<string, CodeGeneratorFunction["JavaScript"]>} customGenerators
*/
export function registerJavaScriptGenerators(language, customGenerators) {
  if(language in Blockly === false) {
    throw new Error(`Blockly does not support ${ language } language`);
  }
  Blockly[language].forBlock["event_run_clicked"] = function (block) {
    var statements_body = Blockly[language].statementToCode(block, "BODY");
    console.log({statements_body});

    // Generate code that both registers the handler and executes it directly
    return `
when("RUN_CLICKED", function() {
${statements_body}});
`;
  };

  Blockly[language].forBlock["event_key_press"] = function (block) {
    var key = block.getFieldValue("KEY");
    var statements_body = Blockly[language].statementToCode(block, "BODY");
    console.log({statements_body});

    // Generate code that registers the handler (but doesn't execute it directly)
    // Pass the key as the third parameter to the when function
    return `
when("KEY_PRESS", function(event) {
  if (event.key === "${key}") {
  ${statements_body}  }
});\n`;
  };

  Blockly[language].forBlock["write_to_output"] = function (block) {
    var message = Blockly[language].valueToCode(block, "MESSAGE", Blockly[language].ORDER_ATOMIC) || "''";
    // For variables, we want to display both the variable name and its value
    if(message.startsWith("\"")) {
      return `appendOutput("${ message.replace(/"/g, "\\\"") }");
`;
    }
    return `appendOutput(${ message });
`;
  };
  for(const key in customGenerators) {
    Blockly[language].forBlock[key] = customGenerators[key];
  }
}
