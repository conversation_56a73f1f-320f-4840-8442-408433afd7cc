/// <reference path="./global.d.ts"/>

/**
 * Monaco Editor integration for the Blockly playground.
 * This module provides functions for initializing and managing Monaco Editor.
 *
 * This is the single source of truth for Monaco Editor functionality.
 */

/**
 * Initialize Monaco Editor in the specified container.
 * @param {HTMLElement} container - The container element for Monaco Editor.
 * @param {Object} options - Options for Monaco Editor.
 * @param {string} options.value - Initial value for the editor.
 * @param {boolean} options.readOnly - Whether the editor should be read-only.
 * @param {string} options.language - The language for the editor (default: 'javascript').
 * @param {string} options.theme - The theme for the editor (default: 'vs-light').
 * @returns {IStandaloneCodeEditor} The Monaco Editor instance.
 */
export function initMonacoEditor(container, options = {}) {

  // Check if monaco is defined
  if (typeof monaco === 'undefined') {
    console.error('Monaco is not defined. Make sure it\'s loaded before calling initMonacoEditor.');
    return null;
  }

  // Check if container is valid
  if (!container || !(container instanceof HTMLElement)) {
    console.error('Invalid container provided to initMonacoEditor:', container);
    return null;
  }

  try {
    // Configure Monaco to use ES5 JavaScript (compatible with the interpreter)
    monaco.languages.typescript.javascriptDefaults.setCompilerOptions({
      target: monaco.languages.typescript.ScriptTarget.ES5,
      allowNonTsExtensions: true,
      allowJs: true,
      checkJs: false,
      noLib: true // Disable built-in libraries to prevent Web API suggestions
    });

    // Configure diagnostics options
    monaco.languages.typescript.javascriptDefaults.setDiagnosticsOptions({
      noSemanticValidation: false,
      noSyntaxValidation: false,
      diagnosticCodesToIgnore: [1108, 1375] // Ignore 'let/const' errors
    });

    // Load the core API definitions
    fetch('/public/src/core/core-api.d.ts')
      .then(response => response.text())
      .then(apiDefinitions => {
        // Add the core API definitions to Monaco
        monaco.languages.typescript.javascriptDefaults.addExtraLib(
          apiDefinitions,
          'core-api.d.ts'
        );
      })
      .catch(error => {
        console.error('Failed to load core API definitions:', error);
      });

    // Default options
    const defaultOptions = {
      value: options.value || '// Code will appear here',
      language: options.language || 'javascript',
      theme: options.theme || 'vs-light',
      readOnly: options.readOnly !== undefined ? options.readOnly : false,
      minimap: { enabled: false },
      scrollBeyondLastLine: false,
      automaticLayout: true,
      fontSize: 14,
      lineNumbers: 'on',
      folding: true,
      renderLineHighlight: 'all',
      // Configure editor features
      hover: { enabled: true },
      parameterHints: { enabled: true },
      quickSuggestions: { other: true, comments: false, strings: false },
      suggestOnTriggerCharacters: true,
      contextmenu: true,
      // Limit suggestions to relevant items
      suggest: {
        showKeywords: true,
        showSnippets: false,
        showClasses: true,
        showFunctions: true,
        showVariables: true,
        showModules: false,
        showProperties: true,
        showEvents: false,
        showOperators: true,
        showUnits: false,
        showValues: false,
        showConstants: true,
        showEnums: false,
        showEnumMembers: false,
        showColors: false,
        showFiles: false,
        showReferences: false,
        showFolders: false,
        showTypeParameters: false,
        showIssues: false,
        showUsers: false
      },
      scrollbar: {
        useShadows: false,
        verticalScrollbarSize: 10,
        horizontalScrollbarSize: 10,
        alwaysConsumeMouseWheel: false
      }
    };

    // Create Monaco Editor
    const editor = monaco.editor.create(container, defaultOptions);
    return editor;
  } catch (error) {
    console.error('Error creating Monaco Editor:', error);
    return null;
  }
}

/**
 * Set the value of Monaco Editor.
 * @param {monaco.editor.IStandaloneCodeEditor} editor - The Monaco Editor instance.
 * @param {string} value - The value to set.
 */
export function setMonacoValue(editor, value) {
  if (editor) {
    editor.setValue(value);
  }
}

/**
 * Get the value from Monaco Editor.
 * @param {monaco.editor.IStandaloneCodeEditor} editor - The Monaco Editor instance.
 * @returns {string} The editor value.
 */
export function getMonacoValue(editor) {
  if (editor) {
    return editor.getValue();
  }
  return '';
}

/**
 * Set the read-only state of Monaco Editor.
 * @param {monaco.editor.IStandaloneCodeEditor} editor - The Monaco Editor instance.
 * @param {boolean} readOnly - Whether the editor should be read-only.
 */
export function setMonacoReadOnly(editor, readOnly) {
  if (editor) {
    editor.updateOptions({ readOnly });
  }
}

/**
 * Resize Monaco Editor to fit its container.
 * @param {monaco.editor.IStandaloneCodeEditor} editor - The Monaco Editor instance.
 */
export function resizeMonacoEditor(editor) {
  if (editor) {
    editor.layout();
  }
}

/**
 * Dispose Monaco Editor instance.
 * @param {monaco.editor.IStandaloneCodeEditor} editor - The Monaco Editor instance.
 */
export function disposeMonacoEditor(editor) {
  try {
    if (editor) {
      // Get the parent container
      const container = editor.getDomNode()?.parentElement;

      // Dispose the editor
      editor.dispose();

      // Clear the container to ensure all DOM elements are removed
      if (container) {
        container.innerHTML = '';
      }
    }
  } catch (error) {
    console.error("Error disposing Monaco Editor:", error);
  }
}

/**
 * Initialize Monaco Editor when the Code tab is clicked.
 * This function should be called once when the DOM is loaded.
 */
export function setupMonacoEditorInitialization() {
  // Get the tab elements
  const tabCode = document.getElementById('tab-code');
  if (!tabCode) {
    console.error('Code tab not found');
    return;
  }

  // Add click event listener to the Code tab
  tabCode.addEventListener('click', function() {
    // Use setTimeout to ensure the DOM has updated before initializing
    setTimeout(function() {
      initializeMonacoEditorInContainer();
    }, 0);
  });

  // Check if we should initialize Monaco Editor immediately
  // (if Code tab is active or blocks tab is disabled)
  const urlParams = new URLSearchParams(window.location.search);
  const showBlocksTab = urlParams.get('blocks') !== 'false';

  if (!showBlocksTab || tabCode.classList.contains('tab-active')) {
    // Use setTimeout to ensure the DOM is fully rendered
    setTimeout(initializeMonacoEditorInContainer, 500);
  }
}

/**
 * Initialize Monaco Editor in the monaco-container element.
 * @returns {Promise<void>} A promise that resolves when Monaco is ready
 */
export function initializeMonacoEditorInContainer() {
  return new Promise((resolve, reject) => {
    try {
      // Get the container element
      const container = document.getElementById('monaco-container');
      if (!container) {
        console.error('Monaco container not found');
        // Create the container if it doesn't exist
        const codeDiv = document.getElementById('codeDiv');
        if (codeDiv) {
          const newContainer = document.createElement('div');
          newContainer.id = 'monaco-container';
          newContainer.style.width = '100%';
          newContainer.style.height = '100%';
          codeDiv.appendChild(newContainer);
          console.log('Created monaco-container');
        } else {
          console.error('Code div not found, cannot create monaco-container');
          reject(new Error('Monaco container and code div not found'));
          return;
        }
      }

      // Get the container again (in case we just created it)
      const finalContainer = document.getElementById('monaco-container');
      if (!finalContainer) {
        console.error('Monaco container still not found after creation attempt');
        reject(new Error('Monaco container not found after creation attempt'));
        return;
      }

      // If we already have a Monaco Editor instance, just make it visible and resize it
      if (window.monacoEditor) {
        // Make sure the editor is in the right container
        if (finalContainer.querySelector('.monaco-editor')) {
          // Force a layout update with a slight delay to ensure container is fully rendered
          setTimeout(function() {
            // Update the editor layout
            window.monacoEditor.layout();
            console.log('Monaco editor layout updated');

            // Dispatch an event to notify that Monaco is ready
            window.dispatchEvent(new CustomEvent('monaco-ready'));
            resolve();
          }, 50);
          return;
        }

        // If the editor exists but is not in the container, we need to dispose it
        // This can happen if the DOM was modified
        try {
          window.monacoEditor.dispose();
        } catch (e) {
          console.error('Error disposing editor:', e);
        }
        window.monacoEditor = null;
      }

      // Check if Monaco is already loaded
      if (typeof monaco === 'undefined') {
        // Load Monaco Editor
        require(['vs/editor/editor.main'], function() {
          createEditorInContainer(finalContainer);

          // Dispatch an event to notify that Monaco is ready
          window.dispatchEvent(new CustomEvent('monaco-ready'));
          resolve();
        });
      } else {
        createEditorInContainer(finalContainer);

        // Dispatch an event to notify that Monaco is ready
        window.dispatchEvent(new CustomEvent('monaco-ready'));
        resolve();
      }
    } catch (error) {
      console.error('Error initializing Monaco editor:', error);
      reject(error);
    }
  });
}

/**
 * Create a Monaco Editor instance in the specified container.
 * @param {HTMLElement} container - The container element for Monaco Editor.
 */
function createEditorInContainer(container) {
  // Clear the container
  container.innerHTML = '';

  // Create the editor
  try {
    // Only create a new editor if one doesn't already exist
    if (!window.monacoEditor) {
      // Get read-only state from playground configuration if available
      let readOnly = true; // Default to read-only
      if (window.globalPlayground && window.globalPlayground.getConfig) {
        const config = window.globalPlayground.getConfig();
        readOnly = config.readOnlyCode === true;
      } else {
        // Fallback: Check URL parameters directly if playground not available yet
        const urlParams = new URLSearchParams(window.location.search);
        if (urlParams.get("blocks") === "false") {
          readOnly = false; // Blocks disabled, code should be editable
        } else if (urlParams.get("code") === "true") {
          readOnly = false; // Code explicitly enabled, should be editable
        }
      }

      // Initialize the editor with appropriate options
      window.monacoEditor = initMonacoEditor(container, {
        value: '// Run button clicked event.\n',
        readOnly: readOnly,
        automaticLayout: false // Disable automatic layout to prevent expansion issues
      });

      // Add resize event listener (only once)
      if (!window.monacoResizeListenerAdded) {
        window.addEventListener('resize', function() {
          if (window.monacoEditor) {
            // Use debounce technique to avoid too many layout calls
            clearTimeout(window.monacoResizeTimeout);
            window.monacoResizeTimeout = setTimeout(function() {
              window.monacoEditor.layout();
            }, 100);
          }
        });
        window.monacoResizeListenerAdded = true;
      }

      // Generate initial code if needed
      if (window.updateGeneratedCode && typeof window.updateGeneratedCode === 'function') {
        window.updateGeneratedCode();

        // Add a listener to update the layout after code changes
        window.monacoEditor.onDidChangeModelContent(function() {
          // Force a layout update after content changes
          setTimeout(function() {
            if (window.monacoEditor) {
              window.monacoEditor.layout();
            }
          }, 10);
        });
      }
    } else {
      // Force a layout update with a slight delay to ensure container is fully rendered
      setTimeout(function() {
        // Update the editor layout
        window.monacoEditor.layout();

        // Apply another layout update after a short delay to handle any delayed rendering
        setTimeout(function() {
          if (window.monacoEditor) {
            window.monacoEditor.layout();
          }
        }, 100);
      }, 50);
    }
  } catch (error) {
    console.error('Error creating Monaco Editor:', error);
  }
}
