// Blockly is available globally in the project
// @ts-ignore
import TapeArchive from './tape-archive.js';

/**
 * Utility class for loading and saving playground data
 * Handles saving and loading workspaces, code, and playground configurations
 * in a structured archive format using the TAR format
 */
export default class LoadSaveManager {
  /**
   * Create a new LoadSaveManager
   * @param {Object} options - Configuration options
   * @param {() => void} options.getWorkspaces - Function that returns all workspaces
   * @param {() => void} options.getActiveWorkspaceName - Function that returns the active workspace name
   * @param {() => void} options.setActiveWorkspace - Function to set the active workspace
   * @param {() => void} options.loadWorkspace - Function to load a workspace from XML
   * @param {() => void} options.getPlaygroundType - Function that returns the current playground type (grid, positional, etc.)
   * @param {() => void} options.getEntities - Function that returns the list of entities in the playground
   */
  constructor(options = {}) {
    this.options = {
      getWorkspaces: () => [],
      getActiveWorkspaceName: () => "default",
      setActiveWorkspace: () => {},
      loadWorkspace: () => {},
      getPlaygroundType: () => "unknown",
      getEntities: () => [],
      ...options
    };
  }

  /**
   * Create a manifest.json file with project information
   * @returns {Object} The manifest data object
   */
  createManifest() {
    // Get configuration from the playground
    const config = window.globalPlayground ? window.globalPlayground.getConfig() : {
      showBlocksTab: true,
      showCodeTab: true,
      defaultTab: "blocks",
      readOnlyCode: true
    };

    // Get the playground type
    const playgroundType = this.options.getPlaygroundType();

    return {
      version: "0.1.0",
      name: "VIPE Project",
      description: "Created with Visual Interactive Programming Environment",
      timestamp: new Date().toISOString(),
      playgroundType: playgroundType,
      activeWorkspace: this.options.getActiveWorkspaceName(),
      entities: this.options.getEntities(),
      events: this._getEventBlocks(),
      assets: {
        images: [],
        sounds: []
      },
      config: config
    };
  }

  /**
   * Get all event blocks from all workspaces
   * @returns {Object} Object with event names as keys and arrays of event data as values
   * @private
   */
  _getEventBlocks() {
    const events = {};
    const workspaces = this.options.getWorkspaces();

    // Define the event types we want to capture
    const eventTypes = [
      { blockType: "event_run_clicked", eventName: "run_clicked" },
      { blockType: "event_key_press", eventName: "key_press" }
    ];

    for (const [workspaceName, workspace] of workspaces.entries()) {
      // Process each event type
      for (const { blockType, eventName } of eventTypes) {
        // Find all blocks of this event type in the workspace
        const eventBlocks = workspace.getBlocksByType(blockType);

        // Skip if no blocks of this type
        if (eventBlocks.length === 0) continue;

        // Initialize the events array for this event type if needed
        if (!events[eventName]) {
          events[eventName] = [];
        }

        // Process each event block
        eventBlocks.forEach((block, index) => {
          let xmlText;

          // Ensure block.data is properly serialized before exporting
          // This is the root cause fix for the [object Object] issue
          if (block.data && typeof block.data === 'object') {
            // Store the original data object
            const originalData = block.data;

            // Temporarily replace the data with a JSON string
            block.data = JSON.stringify(originalData);

            // Get the XML for this block and its children
            const blockXml = Blockly.Xml.blockToDom(block);

            // Restore the original data object
            block.data = originalData;

            xmlText = Blockly.Xml.domToText(blockXml);
          } else {
            // Get the XML for this block and its children
            const blockXml = Blockly.Xml.blockToDom(block);
            xmlText = Blockly.Xml.domToText(blockXml);
          }

          // Get the generated code for this block
          Blockly.JavaScript.init(workspace);
          const code = Blockly.JavaScript.blockToCode(block);

          // Add to the events object
          events[eventName].push({
            id: block.id,
            workspace: workspaceName,
            index: index,
            xml: xmlText,
            code: code
          });
        });
      }
    }

    return events;
  }

  /**
   * Save the current playground state as a structured archive
   * @returns {TapeArchive} The archive object with files and directories
   */
  saveArchive() {
    // Create a new TapeArchive
    const archive = new TapeArchive();

    // Add manifest.json file
    archive.addFile('manifest.json', JSON.stringify(this.createManifest(), null, 2));

    // Create directory structure
    archive.addFolder('assets');
    archive.addFolder('assets/images');
    archive.addFolder('assets/sounds');
    archive.addFolder('workspaces');
    archive.addFolder('events');
    archive.addFolder('playground');

    // Add playground type
    archive.addFile('playground/type.txt', this.options.getPlaygroundType());

    // Save workspace data
    const workspaces = this.options.getWorkspaces();
    for (const [workspaceName, workspace] of workspaces) {
      // Create a directory for this workspace
      archive.addFolder(`workspaces/${workspaceName}`);

      // Get the XML for this workspace
      const xml = Blockly.Xml.workspaceToDom(workspace);
      const xmlText = Blockly.Xml.domToText(xml);

      // Get the JavaScript code for this workspace
      Blockly.JavaScript.init(workspace);

      // Get the code from the Monaco editor if available (to preserve comments)
      let code = "";
      if (window.monacoEditor && typeof window.getMonacoValue === 'function') {
        code = window.getMonacoValue(window.monacoEditor);
      }

      // If no code from Monaco, generate it from the workspace
      if (!code || code.trim() === "") {
        code = Blockly.JavaScript.workspaceToCode(workspace);
      }

      // Save the XML and JavaScript code
      archive.addFile(`workspaces/${workspaceName}/blocks.xml`, xmlText);
      archive.addFile(`workspaces/${workspaceName}/code.js`, code);
    }

    // Get all events
    const events = this._getEventBlocks();

    // Create directory structure for events
    for (const [eventName, eventData] of Object.entries(events)) {
      // Create a directory for this event type
      archive.addFolder(`events/${eventName}`);

      // Add each event as XML and JS files
      eventData.forEach((event, index) => {
        const indexStr = String(index).padStart(3, '0');
        archive.addFile(`events/${eventName}/${indexStr}.xml`, event.xml);
        archive.addFile(`events/${eventName}/${indexStr}.js`, event.code);
      });
    }

    // Save entity data
    const entities = this.options.getEntities();
    if (entities && entities.length > 0) {
      archive.addFile('playground/entities.json', JSON.stringify(entities, null, 2));
    }

    // Save playground state if available
    if (this.options.getPlaygroundState) {
      const state = this.options.getPlaygroundState();
      if (state) {
        archive.addFile('playground/state.json', JSON.stringify(state, null, 2));
      }
    }

    return archive;
  }

  /**
   * Create an archive file from the TapeArchive
   * @param {TapeArchive} archive - The TapeArchive object
   * @returns {Blob} A Blob containing the TAR archive file
   */
  createArchiveFile(archive) {
    // Convert the TapeArchive to a Uint8Array
    const binaryData = archive.toUint8Array();

    // Create a Blob from the Uint8Array
    return new Blob([binaryData], { type: "application/x-vipe-archive" });
  }

  /**
   * Save the current VIPE state to a .vipar file and download it
   * The .vipar file is a TAR archive with a custom extension for Visual Interactive Programming Environment exports
   */
  saveToFile() {
    try {
      const archive = this.saveArchive();
      const archiveBlob = this.createArchiveFile(archive);

      // Create a download link
      const url = URL.createObjectURL(archiveBlob);
      const a = document.createElement("a");
      a.href = url;
      a.download = `vipe-export-${new Date().toISOString().slice(0, 10)}.vipar`;
      document.body.appendChild(a);
      a.click();

      // Clean up
      setTimeout(() => {
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
      }, 0);
    } catch (error) {
      console.error("Error saving to file:", error);
      alert("Failed to save playground. See console for details.");
    }
  }



  /**
   * Load playground data from a TapeArchive object
   * @param {TapeArchive} archive - The loaded TapeArchive
   * @returns {Promise<boolean>} Promise that resolves to true if load was successful
   */
  async loadArchive(archive) {
    try {

      // Get the manifest.json file
      const manifestEntry = archive.getEntry('manifest.json');
      if (!manifestEntry || manifestEntry.isDir) {
        console.error("Invalid archive format: manifest.json not found or is a directory");
        return false;
      }

      // Parse the manifest.json file
      const manifest = JSON.parse(manifestEntry.content);

      // Validate the manifest data
      if (!manifest || !manifest.playgroundType) {
        console.error("Invalid manifest format");
        return false;
      }

      // Check if the playground type matches the current environment
      const currentPlaygroundType = this.options.getPlaygroundType ?
        this.options.getPlaygroundType() :
        (window.globalPlayground && window.globalPlayground.type ? window.globalPlayground.type : "unknown");

      if (manifest.playgroundType !== currentPlaygroundType) {
        // Show a dialog to confirm loading a different playground type
        const loadDifferentType = await this._showPlaygroundTypeMismatchDialog(
          manifest.playgroundType,
          currentPlaygroundType
        );

        if (!loadDifferentType) {
          // User canceled loading a different playground type
          return false;
        }
      }

      // Apply configuration settings if present
      if (manifest.config) {
        const configApplied = await this._applyConfiguration(manifest.config);
        if (!configApplied) {
          // Configuration was not applied (user canceled or page is reloading)
          return false;
        }
      }

      // Get all workspaces
      const workspaces = this.options.getWorkspaces();

      // We'll clear workspaces only once before processing all events
      for (const [_, workspace] of workspaces.entries()) {
        console.log('Clearing workspace before loading');
        workspace.clear();
      }

      // Get all entries in the archive
      const entries = archive.list();

      // Process event files
      const eventEntries = entries.filter(path => path.startsWith('events/') && !path.endsWith('/'));
      const eventsByType = {};

      // Group event files by type
      for (const path of eventEntries) {
        // Parse the path: events/[eventType]/[index].[ext]
        const parts = path.split('/');
        if (parts.length !== 3) continue;

        const eventType = parts[1];
        const fileName = parts[2];

        // Initialize the event type array if it doesn't exist
        if (!eventsByType[eventType]) {
          eventsByType[eventType] = [];
        }

        // Get the file content
        const entry = archive.getEntry(path);
        if (!entry || entry.isDir) continue;

        // Parse the file name to get the index and extension
        const fileNameParts = fileName.split('.');
        if (fileNameParts.length !== 2) continue;

        const index = fileNameParts[0];
        const ext = fileNameParts[1];

        // Find or create the event object
        let event = eventsByType[eventType].find(e => e.index === index);
        if (!event) {
          event = { index, workspace: "default" };
          eventsByType[eventType].push(event);
        }

        // Add the content based on the extension
        if (ext === 'xml') {
          event.xml = entry.content;
        } else if (ext === 'js') {
          event.code = entry.content;
        }
      }

      // Import events
      for (const [eventType, events] of Object.entries(eventsByType)) {
        console.log(`Loading ${events.length} events of type ${eventType}`);

        // Process each event
        events.forEach(event => {
          // Skip events without XML or with invalid XML
          if (!event.xml) {
            console.warn(`Event ${eventType}/${event.index} has no XML, skipping`);
            return;
          }

          // Validate XML format
          if (!event.xml.trim().startsWith('<') || !event.xml.trim().endsWith('>')) {
            console.warn(`Event ${eventType}/${event.index} has invalid XML format, skipping`);
            console.warn('XML content:', event.xml);
            return;
          }

          // Get the workspace name
          const workspaceName = event.workspace || "default";

          // Find the workspace
          let workspace;
          for (const [wsName, ws] of workspaces.entries()) {
            if (wsName === workspaceName) {
              workspace = ws;
              break;
            }
          }

          if (!workspace) {
            console.warn(`Workspace '${workspaceName}' not found, using default`);
            let defaultWorkspace = null;

            // Find the default workspace
            for (const [wsName, ws] of workspaces.entries()) {
              if (wsName === "default") {
                defaultWorkspace = ws;
                break;
              }
            }

            // Skip if no default workspace found
            if (!defaultWorkspace) {
              console.error("No default workspace found, skipping event");
              return; // Skip this event in the forEach loop
            }

            workspace = defaultWorkspace;
          }

          // Load the XML
          try {
            console.log(`Loading XML for event ${eventType}/${event.index}:`, event.xml);

            // Use Blockly.utils.xml for parsing XML text to DOM
            const xml = Blockly.utils.xml.textToDom(event.xml);
            console.log('XML parsed successfully:', xml);

            // Don't clear the workspace for each event
            // Instead, we'll position blocks to avoid overlapping
            if (eventType === 'run_clicked' && events.length > 1) {
              if (events.indexOf(event) > 0) {
                console.log('Multiple run_clicked events found, positioning to avoid overlap');
                // Get the XML root element
                const xmlElement = xml.documentElement || xml;

                // Add position attributes to avoid overlapping
                // Each subsequent block will be positioned below the previous one
                const yOffset = events.indexOf(event) * 150; // 150 pixels between blocks
                xmlElement.setAttribute('x', '20');
                xmlElement.setAttribute('y', `${20 + yOffset}`);
              }
            }

            // Create a workspace XML element to wrap the block
            const workspaceXml = Blockly.utils.xml.createElement('xml');
            workspaceXml.appendChild(xml.cloneNode(true));

            // For debugging
            console.log('Final XML to load:', Blockly.Xml.domToText(workspaceXml));

            // Load the XML into the workspace
            try {
              // Try the standard method first
              Blockly.Xml.domToWorkspace(workspaceXml, workspace);
              console.log('XML loaded into workspace successfully using domToWorkspace');
            } catch (domError) {
              console.error('Error using domToWorkspace:', domError);

              // Fallback: try clearing and then loading
              try {
                if (events.indexOf(event) === 0) {
                  // Only clear for the first event
                  workspace.clear();
                }
                Blockly.Xml.appendDomToWorkspace(workspaceXml, workspace);
                console.log('XML loaded into workspace successfully using appendDomToWorkspace');
              } catch (appendError) {
                console.error('Error using appendDomToWorkspace:', appendError);
                throw appendError; // Re-throw to be caught by the outer catch
              }
            }
          } catch (xmlError) {
            console.error(`Error loading XML for event ${eventType}/${event.index}:`, xmlError);
          }
        });
      }

      // Set the active workspace if it exists
      if (manifest.activeWorkspace && this.options.setActiveWorkspace) {
        this.options.setActiveWorkspace(manifest.activeWorkspace);
      }

      // Resize and refresh Blockly workspace after import
      // Use multiple timeouts to ensure everything is properly initialized
      setTimeout(() => {
        if (window.Blockly && window.globalPlayground && window.globalPlayground.workspace) {
          console.log('First timeout: Resizing workspace');
          // Resize the workspace
          window.Blockly.svgResize(window.globalPlayground.workspace);

          // Force a workspace update
          window.globalPlayground.workspace.render();

          // Schedule another check after a short delay
          setTimeout(() => {
            if (window.Blockly && window.globalPlayground && window.globalPlayground.workspace) {
              console.log('Second timeout: Checking blocks');
              // Check if blocks were actually loaded
              const topBlocks = window.globalPlayground.workspace.getTopBlocks(false);
              console.log(`Workspace has ${topBlocks.length} top-level blocks after import`);

              if (topBlocks.length === 0) {
                console.warn('No blocks found in workspace after import. This may indicate an import problem.');
                // Try to recover by forcing a workspace refresh
                window.globalPlayground.workspace.clear();

                // Re-import the first event as a last resort
                for (const [_, events] of Object.entries(eventsByType)) {
                  if (events.length > 0 && events[0].xml) {
                    try {
                      console.log('Attempting recovery by reimporting the first event');

                      // Parse the XML
                      const xml = Blockly.utils.xml.textToDom(events[0].xml);

                      // Create a workspace XML element to wrap the block
                      const workspaceXml = Blockly.utils.xml.createElement('xml');
                      workspaceXml.appendChild(xml.cloneNode(true));

                      // For debugging
                      console.log('Recovery XML to load:', Blockly.Xml.domToText(workspaceXml));

                      // Load the XML into the workspace
                      Blockly.Xml.domToWorkspace(workspaceXml, window.globalPlayground.workspace);
                    } catch (e) {
                      console.error('Recovery attempt failed:', e);
                    }
                  }
                }
              } else {
                // Center the workspace on the blocks
                window.globalPlayground.workspace.scrollCenter();
                console.log('Blocks are visible and workspace is centered');
              }

              // Final check and update
              setTimeout(() => {
                if (window.Blockly && window.globalPlayground && window.globalPlayground.workspace) {
                  console.log('Final timeout: Ensuring workspace is properly rendered');
                  window.Blockly.svgResize(window.globalPlayground.workspace);
                  window.globalPlayground.workspace.render();

                  // Check again if blocks are visible
                  const finalBlocks = window.globalPlayground.workspace.getTopBlocks(false);
                  console.log(`Final check: Workspace has ${finalBlocks.length} top-level blocks`);

                  // Update the code display
                  if (window.updateGeneratedCode && typeof window.updateGeneratedCode === 'function') {
                    console.log('Updating code display after loading file');
                    window.updateGeneratedCode();
                  }
                }
              }, 200);
            }
          }, 200);
        }
      }, 200);

      return true;
    } catch (error) {
      console.error("Error importing archive:", error);
      return false;
    }
  }

  /**
   * Read a playground file and convert it to a TapeArchive
   * @param {File} file - The file to read
   * @returns {Promise<TapeArchive>} Promise that resolves to a TapeArchive
   */
  async readPlaygroundFile(file) {
    try {
      // Check if the file is a VIPAR file
      if (file.name.endsWith('.vipar')) {
        // Read the file as an ArrayBuffer
        const arrayBuffer = await file.arrayBuffer();

        // Create a TapeArchive from the ArrayBuffer
        return new TapeArchive(arrayBuffer);
      } else {
        throw new Error(`Unsupported file type: ${file.name}. Expected .vipar file extension.`);
      }
    } catch (error) {
      console.error('Error reading playground file:', error);
      throw error;
    }
  }

  /**
   * Load playground data from a file
   * @param {File} file - The file to load
   * @returns {Promise<Object>} Promise that resolves to an object with load result information
   */
  async loadFromFile(file) {
    try {
      // Use the user-implemented method to read the file
      const archive = await this.readPlaygroundFile(file);

      // Get the manifest to check the playground type
      const manifestEntry = archive.getEntry('manifest.json');
      if (!manifestEntry || manifestEntry.isDir) {
        console.error("Invalid archive format: manifest.json not found or is a directory");
        return { success: false };
      }

      // Parse the manifest.json file
      const manifest = JSON.parse(manifestEntry.content);

      // Get the saved playground type
      const savedType = manifest.playgroundType;

      // Get the current playground type
      const currentType = this.options.getPlaygroundType ?
        this.options.getPlaygroundType() :
        (window.globalPlayground && window.globalPlayground.type ? window.globalPlayground.type : "unknown");

      // Check for type mismatch
      const typeMismatch = savedType !== currentType;

      // Load the archive
      const success = await this.loadArchive(archive);

      // If loading was successful, update the code display and register event handlers
      if (success) {
        // Use setTimeout to ensure the workspace is fully loaded
        setTimeout(() => {
          // Make sure Monaco editor is initialized
          if (!window.monacoEditor) {
            console.log('Initializing Monaco editor after file load');
            // Import the function from editor.js
            import('../core/editor.js').then(module => {
              // Initialize Monaco editor (returns a promise)
              return module.initializeMonacoEditorInContainer();
            }).then(() => {
              console.log('Monaco editor ready after file load');
              this._generateCodeAndRegisterHandlers();
            }).catch(error => {
              console.error('Error initializing Monaco editor:', error);
              // Still try to generate code even if Monaco initialization fails
              this._generateCodeAndRegisterHandlers();
            });
          } else {
            // Monaco is already initialized, generate code and register handlers
            this._generateCodeAndRegisterHandlers();
          }
        }, 500);
      }

      // Return detailed result information
      return {
        success,
        typeMismatch,
        savedType,
        currentType
      };
    } catch (error) {
      console.error("Error loading from file:", error);
      alert(`Load failed: ${error.message}`);
      return { success: false };
    }
  }

  /**
   * Show a file picker dialog and load from the selected file
   * @returns {Promise<Object>} Promise that resolves to an object with load result information
   */
  async showLoadDialog() {
    return new Promise((resolve) => {
      // Create a file input element
      const input = document.createElement("input");
      input.type = "file";
      input.accept = ".vipar"; // Only accept VIPE archive files

      input.onchange = async (event) => {
        const file = event.target.files[0];
        if (!file) {
          resolve({ success: false });
          return;
        }

        try {
          const result = await this.loadFromFile(file);
          resolve(result);
        } catch (error) {
          console.error("Load failed:", error);
          alert(`Load failed: ${error.message}`);
          resolve({ success: false });
        }
      };

      // Trigger the file picker
      input.click();
    });
  }

  /**
   * Check if the current configuration matches the saved configuration
   * @param {Object} config - Configuration settings from the manifest
   * @returns {Object} Object with mismatch information
   * @private
   */
  _checkConfigurationMismatch(config) {
    // Get current configuration from the playground
    const currentConfig = window.globalPlayground ? window.globalPlayground.getConfig() : {
      showBlocksTab: true,
      showCodeTab: true,
      defaultTab: "blocks",
      readOnlyCode: true
    };

    // Check for mismatches
    const mismatches = [];

    if (config.showBlocksTab !== undefined && config.showBlocksTab !== currentConfig.showBlocksTab) {
      mismatches.push({
        setting: "showBlocksTab",
        saved: config.showBlocksTab,
        current: currentConfig.showBlocksTab,
        description: config.showBlocksTab ?
          "The project was saved with blocks enabled, but blocks are currently disabled." :
          "The project was saved with blocks disabled, but blocks are currently enabled."
      });
    }

    if (config.showCodeTab !== undefined && config.showCodeTab !== currentConfig.showCodeTab) {
      mismatches.push({
        setting: "showCodeTab",
        saved: config.showCodeTab,
        current: currentConfig.showCodeTab,
        description: config.showCodeTab ?
          "The project was saved with code editor enabled, but code editor is currently disabled." :
          "The project was saved with code editor disabled, but code editor is currently enabled."
      });
    }

    return {
      hasMismatch: mismatches.length > 0,
      mismatches: mismatches
    };
  }

  /**
   * Show a confirmation dialog for configuration mismatches
   * @param {Array} mismatches - Array of configuration mismatches
   * @returns {Promise<string>} Promise that resolves to "apply", "keep", or "cancel"
   * @private
   */
  _showConfigMismatchDialog(mismatches) {
    return new Promise((resolve) => {
      // Create dialog elements
      const dialog = document.createElement("div");
      dialog.className = "modal-box config-mismatch-dialog";
      dialog.style.position = "fixed";
      dialog.style.top = "50%";
      dialog.style.left = "50%";
      dialog.style.transform = "translate(-50%, -50%)";
      dialog.style.zIndex = "1000";
      dialog.style.maxWidth = "640px";
      dialog.style.width = "90%";

      // Add title
      const title = document.createElement("h3");
      title.textContent = "Configuration Mismatch";
      title.className = "font-bold text-lg";
      dialog.appendChild(title);

      // Add description
      const description = document.createElement("p");
      description.textContent = "The project you're loading was saved with different configuration settings than your current environment.";
      description.className = "py-4";
      dialog.appendChild(description);

      // Add mismatches list
      const list = document.createElement("ul");
      list.className = "list-disc pl-5 mb-4 text-base-content";

      mismatches.forEach(mismatch => {
        const item = document.createElement("li");
        item.textContent = mismatch.description;
        item.className = "mb-1";
        list.appendChild(item);
      });

      dialog.appendChild(list);

      // Add warning
      const warning = document.createElement("div");
      warning.className = "alert alert-warning my-4";

      const warningIcon = document.createElement("div");
      warningIcon.innerHTML = `<svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" /></svg>`;

      const warningText = document.createElement("span");
      warningText.textContent = "Applying the saved configuration will update the interface to match the project's settings. Keeping your current configuration might cause some features to not work correctly.";

      warning.appendChild(warningIcon);
      warning.appendChild(warningText);
      dialog.appendChild(warning);

      // Add buttons
      const buttonContainer = document.createElement("div");
      buttonContainer.className = "modal-action";

      const cancelButton = document.createElement("button");
      cancelButton.textContent = "Cancel";
      cancelButton.className = "btn btn-sm";
      cancelButton.onclick = () => {
        document.body.removeChild(dialog);
        document.body.removeChild(overlay);
        resolve("cancel");
      };

      const keepButton = document.createElement("button");
      keepButton.textContent = "Keep Current Settings";
      keepButton.className = "btn btn-sm";
      keepButton.style.backgroundColor = "#9E9E9E";
      keepButton.style.color = "white";
      keepButton.onclick = () => {
        document.body.removeChild(dialog);
        document.body.removeChild(overlay);
        resolve("keep");
      };

      const applyButton = document.createElement("button");
      applyButton.textContent = "Use Project Settings";
      applyButton.className = "btn btn-sm";
      applyButton.style.background = "var(--gradient)";
      applyButton.style.color = "white";
      applyButton.onclick = () => {
        document.body.removeChild(dialog);
        document.body.removeChild(overlay);
        resolve("apply");
      };

      buttonContainer.appendChild(cancelButton);
      buttonContainer.appendChild(keepButton);
      buttonContainer.appendChild(applyButton);
      dialog.appendChild(buttonContainer);

      // Create overlay
      const overlay = document.createElement("div");
      overlay.className = "modal-backdrop";
      overlay.style.position = "fixed";
      overlay.style.top = "0";
      overlay.style.left = "0";
      overlay.style.width = "100%";
      overlay.style.height = "100%";
      overlay.style.backgroundColor = "rgba(0, 0, 0, 0.5)";
      overlay.style.zIndex = "999";

      // Add to document
      document.body.appendChild(overlay);
      document.body.appendChild(dialog);
    });
  }

  /**
   * Apply configuration settings from the manifest
   * @param {Object} config - Configuration settings
   * @returns {Promise<boolean>} Promise that resolves to true if configuration was applied, false otherwise
   * @private
   */
  async _applyConfiguration(config) {
    // Check for configuration mismatches
    const mismatchInfo = this._checkConfigurationMismatch(config);

    if (mismatchInfo.hasMismatch) {
      // Show confirmation dialog
      const action = await this._showConfigMismatchDialog(mismatchInfo.mismatches);

      if (action === "cancel") {
        // User canceled the operation
        return false;
      } else if (action === "apply") {
        // Apply saved configuration directly without reloading
        if (window.globalPlayground && typeof window.globalPlayground.setConfig === 'function') {
          // Apply the configuration directly
          window.globalPlayground.setConfig(config);

          // Update UI elements directly
          const tabBlockly = document.getElementById("tab-blockly");
          const tabCode = document.getElementById("tab-code");
          const blocklyDiv = document.getElementById("blocklyDiv");
          const codeDiv = document.getElementById("codeDiv");

          if (tabBlockly && tabCode && blocklyDiv && codeDiv) {
            // Apply visibility changes based on configuration
            if (config.showBlocksTab === false) {
              tabBlockly.style.display = "none";
              // If blocks tab is hidden, activate code tab
              tabCode.classList.add("tab-active");
              tabBlockly.classList.remove("tab-active");
              codeDiv.classList.remove("hidden");
              codeDiv.classList.add("block");
              blocklyDiv.classList.add("hidden");
              blocklyDiv.classList.remove("block");
            } else {
              tabBlockly.style.display = "";
            }

            if (config.showCodeTab === false) {
              tabCode.style.display = "none";
              // If code tab is hidden, activate blocks tab
              tabBlockly.classList.add("tab-active");
              tabCode.classList.remove("tab-active");
              blocklyDiv.classList.remove("hidden");
              blocklyDiv.classList.add("block");
              codeDiv.classList.add("hidden");
              codeDiv.classList.remove("block");
            } else {
              tabCode.style.display = "";
            }

            // Update Monaco editor read-only state
            if (window.monacoEditor && window.monacoEditor.updateOptions) {
              window.monacoEditor.updateOptions({ readOnly: config.readOnlyCode === true });
            }
          }

          // Update the code display and register event handlers if needed
          setTimeout(() => {
            // Make sure Monaco editor is initialized
            if (!window.monacoEditor) {
              console.log('Initializing Monaco editor after applying configuration');
              // Import the function from editor.js
              import('../core/editor.js').then(module => {
                // Initialize Monaco editor (returns a promise)
                return module.initializeMonacoEditorInContainer();
              }).then(() => {
                console.log('Monaco editor ready after applying configuration');
                this._generateCodeAndRegisterHandlers();
              }).catch(error => {
                console.error('Error initializing Monaco editor:', error);
                // Still try to generate code even if Monaco initialization fails
                this._generateCodeAndRegisterHandlers();
              });
            } else {
              // Monaco is already initialized, generate code and register handlers
              this._generateCodeAndRegisterHandlers();
            }
          }, 300);
        }

        return true; // Continue with loading the file
      }
      // If action is "keep", we continue with the current configuration
      else if (action === "keep") {
        // Don't apply the saved configuration, just continue with loading the file
        return true;
      }
    }

    // Only apply configuration to the playground if we're not in "keep" mode
    if (window.globalPlayground && typeof window.globalPlayground.setConfig === 'function') {
      window.globalPlayground.setConfig(config);
    }

    return true;
  }

  /**
   * Show a dialog informing the user they need to switch playground types
   * @param {string} savedType - The playground type from the saved file
   * @param {string} currentType - The current playground type
   * @returns {Promise<boolean>} Promise that always resolves to false (don't load)
   * @private
   */
  _showPlaygroundTypeMismatchDialog(savedType, currentType) {
    return new Promise((resolve) => {
      // Create dialog elements
      const dialog = document.createElement("div");
      dialog.className = "modal-box playground-type-mismatch-dialog";
      dialog.style.position = "fixed";
      dialog.style.top = "50%";
      dialog.style.left = "50%";
      dialog.style.transform = "translate(-50%, -50%)";
      dialog.style.zIndex = "1000";
      dialog.style.maxWidth = "640px";
      dialog.style.width = "90%";

      // Add title
      const title = document.createElement("h3");
      title.textContent = "Playground Type Mismatch";
      title.className = "font-bold text-lg";
      dialog.appendChild(title);

      // Add description
      const description = document.createElement("p");
      description.textContent = `The project you're trying to load was created in a different playground environment.`;
      description.className = "py-4";
      dialog.appendChild(description);

      // Add details
      const details = document.createElement("div");
      details.className = "mb-4";

      // Format the playground type names to be more user-friendly
      const formatTypeName = (type) => {
        if (type === "grid") return "Grid Playground";
        if (type === "positional") return "Positional Playground";
        return type.charAt(0).toUpperCase() + type.slice(1) + " Playground";
      };

      const savedTypeInfo = document.createElement("p");
      savedTypeInfo.innerHTML = `<strong>Project type:</strong> ${formatTypeName(savedType)}`;
      savedTypeInfo.className = "mb-2";

      const currentTypeInfo = document.createElement("p");
      currentTypeInfo.innerHTML = `<strong>Current environment:</strong> ${formatTypeName(currentType)}`;
      currentTypeInfo.className = "mb-2";

      details.appendChild(savedTypeInfo);
      details.appendChild(currentTypeInfo);
      dialog.appendChild(details);

      // Add instruction
      const instruction = document.createElement("div");
      instruction.className = "alert alert-info my-4";

      const infoIcon = document.createElement("div");
      infoIcon.innerHTML = `<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" class="stroke-current shrink-0 w-6 h-6"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>`;

      const instructionText = document.createElement("span");

      // Create specific instructions based on the playground types
      let instructionMessage = "";

      if (savedType === "grid" && currentType === "positional") {
        instructionMessage = "Please switch to the Grid Playground environment before loading this project.";
      } else if (savedType === "positional" && currentType === "grid") {
        instructionMessage = "Please switch to the Positional Playground environment before loading this project.";
      } else {
        instructionMessage = `Please switch to the ${formatTypeName(savedType)} environment before loading this project.`;
      }

      instructionText.textContent = instructionMessage;

      instruction.appendChild(infoIcon);
      instruction.appendChild(instructionText);
      dialog.appendChild(instruction);

      // Add warning
      const warning = document.createElement("div");
      warning.className = "alert alert-warning my-4";

      const warningIcon = document.createElement("div");
      warningIcon.innerHTML = `<svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" /></svg>`;

      const warningText = document.createElement("span");
      warningText.textContent = "Projects can only be loaded in the same type of playground they were created in. Different playground types have incompatible blocks and features.";

      warning.appendChild(warningIcon);
      warning.appendChild(warningText);
      dialog.appendChild(warning);

      // Add buttons
      const buttonContainer = document.createElement("div");
      buttonContainer.className = "modal-action";

      const okButton = document.createElement("button");
      okButton.textContent = "OK";
      okButton.className = "btn btn-sm";
      okButton.style.background = "var(--gradient)";
      okButton.style.color = "white";
      okButton.onclick = () => {
        document.body.removeChild(dialog);
        document.body.removeChild(overlay);
        resolve(false);
      };

      buttonContainer.appendChild(okButton);
      dialog.appendChild(buttonContainer);

      // Create overlay
      const overlay = document.createElement("div");
      overlay.className = "modal-backdrop";
      overlay.style.position = "fixed";
      overlay.style.top = "0";
      overlay.style.left = "0";
      overlay.style.width = "100%";
      overlay.style.height = "100%";
      overlay.style.backgroundColor = "rgba(0, 0, 0, 0.5)";
      overlay.style.zIndex = "999";

      // Add to document
      document.body.appendChild(overlay);
      document.body.appendChild(dialog);
    });
  }

  /**
   * Generate code from blocks and register event handlers
   * @private
   */
  _generateCodeAndRegisterHandlers() {
    // Force code generation from blocks
    if (window.Blockly && window.globalPlayground) {
      const activeWorkspaceName = window.globalPlayground.getActiveWorkspaceName();
      const activeWorkspace = window.globalPlayground.getWorkspace(activeWorkspaceName);

      if (activeWorkspace) {
        // Initialize the JavaScript generator
        window.Blockly.JavaScript.init(activeWorkspace);

        let code = "";

        // Find all "when run clicked" blocks in this workspace
        const runEventBlocks = activeWorkspace.getBlocksByType("event_run_clicked");

        // Find all "key press" blocks in this workspace
        const keyPressBlocks = activeWorkspace.getBlocksByType("event_key_press");

        // Combine all event blocks
        const allEventBlocks = [...runEventBlocks, ...keyPressBlocks];

        if (allEventBlocks.length > 0) {
          // Generate code for each event block in this workspace
          for (let i = 0; i < allEventBlocks.length; i++) {
            const eventBlock = allEventBlocks[i];

            // Add a block identifier
            if (i > 0) {
              code += "\n\n";
            }

            // Get code from this event block
            code += window.Blockly.JavaScript.blockToCode(eventBlock);
          }

          if (code && code.trim() !== "") {
            // Store the generated code for future use
            window.generatedCode = code;

            // Update the Monaco editor if it exists
            if (window.monacoEditor && typeof window.setMonacoValue === 'function') {
              window.setMonacoValue(window.monacoEditor, code);
            }
          }
        }
      }
    }

    // Update the code display
    if (window.updateGeneratedCode && typeof window.updateGeneratedCode === 'function') {
      window.updateGeneratedCode();
    }

    // Pre-register event handlers by running the code once
    if (window.getEditorCode && typeof window.getEditorCode === 'function' &&
        window.globalPlayground && typeof window.globalPlayground.runCode === 'function') {
      const code = window.getEditorCode();
      if (code) {
        // Clear existing event handlers first
        if (window.globalPlayground.clearEventHandlers &&
            typeof window.globalPlayground.clearEventHandlers === 'function') {
          window.globalPlayground.clearEventHandlers();
        }
        // Run the code to register event handlers
        window.globalPlayground.runCode(code).catch(error => {
          console.error('Error pre-registering event handlers:', error);
        });
      }
    }
  }
}
