// @ts-check
/// <reference path="../../../../src/core/global.d.ts"/>

import { appendDebugOutput, appendOutput, appendSimpleOutput } from "../../../utils/output.js";
import BotGame from "../renderer/bot-game.js";
import { initCommonInterpreterFunctions } from "../../../core/interpreter-common.js";

/** @param {BotGame} game */
export default function createPlaygroundAPI(game) {
  // Basic Movement Functions
  function move() {
    try {
      game.move(1);
      appendDebugOutput("Action: Move forward", "info");
    } catch (error) {
      appendSimpleOutput("Cannot move forward!", "error");
      appendDebugOutput(`Action failed: ${error.message}`, "error");
      throw error;
    }
    game.render();
  }

  function turnLeft() {
    try {
      game.turn(1);
      appendDebugOutput("Action: Turn left", "info");
    } catch (error) {
      appendSimpleOutput("Cannot turn left!", "error");
      appendDebugOutput(`Action failed: ${error.message}`, "error");
      throw error;
    }
    game.render();
  }

  function turnRight() {
    try {
      game.turn(-1);
      appendDebugOutput("Action: Turn right", "info");
    } catch (error) {
      appendSimpleOutput("Cannot turn right!", "error");
      appendDebugOutput(`Action failed: ${error.message}`, "error");
      throw error;
    }
    game.render();
  }

  // Beeper Interaction Functions
  function hasBeepers() {
    try {
      const hasBeepers = game.countBeeper() > 0;
      appendDebugOutput(`Check: Has beepers at current location: ${hasBeepers}`, "info");
      return hasBeepers;
    } catch (error) {
      appendSimpleOutput("Cannot check for beepers!", "error");
      appendDebugOutput(`Action failed: ${error.message}`, "error");
      throw error;
    }
  }

  function pickBeeper() {
    try {
      const success = game.pickBeeper();
      if (success) {
        appendDebugOutput("Action: Picked up a beeper", "info");
        appendSimpleOutput("Picked up a beeper", "success");
      } else {
        appendDebugOutput("Action failed: No beeper to pick up", "warning");
        appendSimpleOutput("No beeper to pick up", "warning");
      }
    } catch (error) {
      appendSimpleOutput("Cannot pick up beeper!", "error");
      appendDebugOutput(`Action failed: ${error.message}`, "error");
      throw error;
    }
    game.render();
  }

  function putBeeper() {
    try {
      const success = game.putBeeper();
      if (success) {
        appendDebugOutput("Action: Put down a beeper", "info");
        appendSimpleOutput("Put down a beeper", "success");
      } else {
        appendDebugOutput("Action failed: No beepers to put down", "warning");
        appendSimpleOutput("No beepers to put down", "warning");
      }
    } catch (error) {
      appendSimpleOutput("Cannot put down beeper!", "error");
      appendDebugOutput(`Action failed: ${error.message}`, "error");
      throw error;
    }
    game.render();
  }

  // Wall Detection Functions
  function isLeftBlocked() {
    try {
      // Get the current direction and calculate the left direction
      const currentDir = game.bot.dir;
      // Left is 90 degrees counter-clockwise from current direction
      const leftDir = { x: -currentDir.y, y: currentDir.x };

      // Calculate the position to the left
      const leftPos = {
        x: game.bot.position.x + leftDir.x,
        y: game.bot.position.y + leftDir.y
      };

      // Check if the position is valid and not blocked
      const isBlocked = !game.isPositionValid(leftPos.x, leftPos.y) ||
                        game.isPositionBlocked(leftPos.x, leftPos.y);

      appendDebugOutput(`Check: Is left blocked: ${isBlocked}`, "info");
      return isBlocked;
    } catch (error) {
      appendSimpleOutput("Cannot check if left is blocked!", "error");
      appendDebugOutput(`Action failed: ${error.message}`, "error");
      throw error;
    }
  }

  function isRightBlocked() {
    try {
      // Get the current direction and calculate the right direction
      const currentDir = game.bot.dir;
      // Right is 90 degrees clockwise from current direction
      const rightDir = { x: currentDir.y, y: -currentDir.x };

      // Calculate the position to the right
      const rightPos = {
        x: game.bot.position.x + rightDir.x,
        y: game.bot.position.y + rightDir.y
      };

      // Check if the position is valid and not blocked
      const isBlocked = !game.isPositionValid(rightPos.x, rightPos.y) ||
                        game.isPositionBlocked(rightPos.x, rightPos.y);

      appendDebugOutput(`Check: Is right blocked: ${isBlocked}`, "info");
      return isBlocked;
    } catch (error) {
      appendSimpleOutput("Cannot check if right is blocked!", "error");
      appendDebugOutput(`Action failed: ${error.message}`, "error");
      throw error;
    }
  }

  function isFrontBlocked() {
    try {
      // Get the current direction
      const currentDir = game.bot.dir;

      // Calculate the position in front
      const frontPos = {
        x: game.bot.position.x + currentDir.x,
        y: game.bot.position.y + currentDir.y
      };

      // Check if the position is valid and not blocked
      const isBlocked = !game.isPositionValid(frontPos.x, frontPos.y) ||
                        game.isPositionBlocked(frontPos.x, frontPos.y);

      appendDebugOutput(`Check: Is front blocked: ${isBlocked}`, "info");
      return isBlocked;
    } catch (error) {
      appendSimpleOutput("Cannot check if front is blocked!", "error");
      appendDebugOutput(`Action failed: ${error.message}`, "error");
      throw error;
    }
  }

  function isBackBlocked() {
    try {
      // Get the current direction and calculate the opposite direction
      const currentDir = game.bot.dir;
      // Back is opposite of current direction
      const backDir = { x: -currentDir.x, y: -currentDir.y };

      // Calculate the position behind
      const backPos = {
        x: game.bot.position.x + backDir.x,
        y: game.bot.position.y + backDir.y
      };

      // Check if the position is valid and not blocked
      const isBlocked = !game.isPositionValid(backPos.x, backPos.y) ||
                        game.isPositionBlocked(backPos.x, backPos.y);

      appendDebugOutput(`Check: Is back blocked: ${isBlocked}`, "info");
      return isBlocked;
    } catch (error) {
      appendSimpleOutput("Cannot check if back is blocked!", "error");
      appendDebugOutput(`Action failed: ${error.message}`, "error");
      throw error;
    }
  }

  // Direction Checking Functions
  function isFacingNorth() {
    try {
      const dir = game.bot.dir;
      const isNorth = dir.y === -1 && dir.x === 0;
      appendDebugOutput(`Check: Is facing north: ${isNorth}`, "info");
      return isNorth;
    } catch (error) {
      appendSimpleOutput("Cannot check direction!", "error");
      appendDebugOutput(`Action failed: ${error.message}`, "error");
      throw error;
    }
  }

  function isFacingEast() {
    try {
      const dir = game.bot.dir;
      const isEast = dir.x === 1 && dir.y === 0;
      appendDebugOutput(`Check: Is facing east: ${isEast}`, "info");
      return isEast;
    } catch (error) {
      appendSimpleOutput("Cannot check direction!", "error");
      appendDebugOutput(`Action failed: ${error.message}`, "error");
      throw error;
    }
  }

  function isFacingSouth() {
    try {
      const dir = game.bot.dir;
      const isSouth = dir.y === 1 && dir.x === 0;
      appendDebugOutput(`Check: Is facing south: ${isSouth}`, "info");
      return isSouth;
    } catch (error) {
      appendSimpleOutput("Cannot check direction!", "error");
      appendDebugOutput(`Action failed: ${error.message}`, "error");
      throw error;
    }
  }

  function isFacingWest() {
    try {
      const dir = game.bot.dir;
      const isWest = dir.x === -1 && dir.y === 0;
      appendDebugOutput(`Check: Is facing west: ${isWest}`, "info");
      return isWest;
    } catch (error) {
      appendSimpleOutput("Cannot check direction!", "error");
      appendDebugOutput(`Action failed: ${error.message}`, "error");
      throw error;
    }
  }

  // Utility Functions
  function exit(statusCode = 0) {
    appendDebugOutput(`Program exited with status code: ${statusCode}`, "info");
    appendSimpleOutput(`Program exited with status code: ${statusCode}`, "warning");
    // We don't actually exit the program, just log the exit
  }

  /**
   * Log messages to the output console
   * @param {any} value - Value to log
   */
  function log(value) {
    const message = String(value);
    appendDebugOutput(`Log: ${message}`, "info");
    appendSimpleOutput(message, "info");
  }

  function input(message = "") {
    // Since we can't actually get input in this environment, we'll just log it
    appendDebugOutput(`Input requested with prompt: ${message}`, "info");
    appendSimpleOutput(`Input requested: ${message}`, "warning");
    // Return a default value
    return "";
  }

  /**
   * Check if the bot has reached the destination
   * @returns {boolean} True if the bot is at the destination, false otherwise
   */
  function isAtDestination() {
    // Check if the bot's position matches the destination position
    return game.countBeeper() > 0;
  }

  /**
   * @param {InterpreterInstance} interpreter
   * @param {typeof InterpreterGlobalScopeObject} globalObject
  */
  function init(interpreter, globalObject) {
    // Register basic movement functions
    interpreter.setProperty(globalObject, "move", interpreter.createNativeFunction(move));
    interpreter.setProperty(globalObject, "turnLeft", interpreter.createNativeFunction(turnLeft));
    interpreter.setProperty(globalObject, "turnRight", interpreter.createNativeFunction(turnRight));

    // Register beeper interaction functions
    interpreter.setProperty(globalObject, "hasBeepers", interpreter.createNativeFunction(hasBeepers));
    interpreter.setProperty(globalObject, "pickBeeper", interpreter.createNativeFunction(pickBeeper));
    interpreter.setProperty(globalObject, "putBeeper", interpreter.createNativeFunction(putBeeper));

    // Register wall detection functions
    interpreter.setProperty(globalObject, "isLeftBlocked", interpreter.createNativeFunction(isLeftBlocked));
    interpreter.setProperty(globalObject, "isRightBlocked", interpreter.createNativeFunction(isRightBlocked));
    interpreter.setProperty(globalObject, "isFrontBlocked", interpreter.createNativeFunction(isFrontBlocked));
    interpreter.setProperty(globalObject, "isBackBlocked", interpreter.createNativeFunction(isBackBlocked));

    // Register direction checking functions
    interpreter.setProperty(globalObject, "isFacingNorth", interpreter.createNativeFunction(isFacingNorth));
    interpreter.setProperty(globalObject, "isFacingEast", interpreter.createNativeFunction(isFacingEast));
    interpreter.setProperty(globalObject, "isFacingSouth", interpreter.createNativeFunction(isFacingSouth));
    interpreter.setProperty(globalObject, "isFacingWest", interpreter.createNativeFunction(isFacingWest));

    // Register utility functions
    interpreter.setProperty(globalObject, "exit", interpreter.createNativeFunction(exit));
    interpreter.setProperty(globalObject, "log", interpreter.createNativeFunction(log));
    interpreter.setProperty(globalObject, "input", interpreter.createNativeFunction(input));

    // Register legacy functions for backward compatibility
    interpreter.setProperty(globalObject, "isAtDestination", interpreter.createNativeFunction(isAtDestination));

    // Initialize common interpreter functions
    initCommonInterpreterFunctions(interpreter, globalObject);
  }

  const api = {
    init,
    methods: {
      // Basic Movement
      move, turnLeft, turnRight,
      // Beeper Interaction
      hasBeepers, pickBeeper, putBeeper,
      // Wall Detection
      isLeftBlocked, isRightBlocked, isFrontBlocked, isBackBlocked,
      // Direction Checking
      isFacingNorth, isFacingEast, isFacingSouth, isFacingWest,
      // Utility Functions
      exit, log, input,
      // Legacy Functions
      isAtDestination
    }
  };
  return api;
}
