import { appendDebugOutput, appendSimpleOutput, clearOutput } from "../utils/output.js";
import {
  getMonacoValue,
  setMonacoValue,
  setupMonacoEditorInitialization
} from "./editor.js";
import { unfocusBlocklyFields } from "../utils/blockly-utils.js";
import { logFileHandlingSupport } from "../utils/feature-detection.js";
import {
  initFileHandling,
  wasOpenedViaFileHandling,
  loadPendingFile,
  checkPendingFileAfterConfigChange
} from "../utils/file-handler.js";

// Make setMonacoValue available globally
window.setMonacoValue = setMonacoValue;
// Make unfocusBlocklyFields available globally
window.unfocusBlocklyFields = unfocusBlocklyFields;

import LoadSaveManager from "../utils/import-export.js";
import Playground from "./playground.js";

document.addEventListener("DOMContentLoaded", function () {
  // Ensure light theme is applied
  document.documentElement.setAttribute("data-theme", "light");

  // Initialize Monaco Editor
  setupMonacoEditorInitialization();

  // Log file handling support details
  const fileHandlingSupport = logFileHandlingSupport();

  // Check if we were opened via file-handler.html
  if (wasOpenedViaFileHandling()) {
    console.log("Page was opened via file handling, checking for pending file");

    // Check for a pending file in localStorage
    setTimeout(() => {
      try {
        const pendingFileData = localStorage.getItem("vipe_pending_file");
        if (pendingFileData) {
          const pendingFile = JSON.parse(pendingFileData);

          // Check if the pending file is recent (less than 30 seconds old)
          const now = Date.now();
          if (pendingFile.timestamp && (now - pendingFile.timestamp < 30000)) {
            console.log("Found pending file to load:", pendingFile.fileName);

            // Wait for the playground to initialize
            setTimeout(() => {
              if (!window.globalPlayground) {
                console.error("Playground not initialized yet, cannot load file");
                return;
              }

              // Convert hex string back to ArrayBuffer
              const hexString = pendingFile.fileData;
              const bytes = new Uint8Array(hexString.length / 2);
              for (let i = 0; i < hexString.length; i += 2) {
                bytes[i / 2] = parseInt(hexString.substr(i, 2), 16);
              }

              // Create a File object
              const file = new File([bytes], pendingFile.fileName, { type: "application/x-vipe-archive" });

              // Create a LoadSaveManager and load the file
              const loadSaveManager = new LoadSaveManager({
                getWorkspaces: () => window.globalPlayground.workspaces,
                getActiveWorkspaceName: () => window.globalPlayground.getActiveWorkspaceName(),
                setActiveWorkspace: (name) => window.globalPlayground.setActiveWorkspace(name),
                getPlaygroundType: () => {
                  // Get the current playground type from the URL
                  const url = window.location.href;
                  if (url.includes('positional.html') || url.includes('/positional')) {
                    return 'positional';
                  } else if (url.includes('grid.html') || url.includes('/grid')) {
                    return 'grid';
                  }
                  return 'grid'; // Default to grid
                },
                getEntities: () => window.globalPlayground.getEntities ? window.globalPlayground.getEntities() : [],
                setEntities: (entities) => {
                  if (window.globalPlayground.setEntities && typeof window.globalPlayground.setEntities === 'function') {
                    window.globalPlayground.setEntities(entities);
                  }
                },
                getPlaygroundState: () => {
                  if (window.globalPlayground.getState && typeof window.globalPlayground.getState === 'function') {
                    return window.globalPlayground.getState();
                  }
                  return null;
                },
                setPlaygroundState: (state) => {
                  if (window.globalPlayground.setState && typeof window.globalPlayground.setState === 'function') {
                    window.globalPlayground.setState(state);
                  }
                }
              });

              // Load the file
              loadSaveManager.loadFromFile(file).then(result => {
                console.log("File load result:", result);

                if (result.success) {
                  // Show success message
                  appendSimpleOutput("Your VIPE project has been loaded successfully!", "success");
                  appendDebugOutput("Load successful: VIPE project loaded from stored file.", "success");
                } else if (result.typeMismatch) {
                  // We're in the wrong playground type, need to redirect again
                  appendSimpleOutput(`Redirecting to the correct playground type (${result.savedType})...`, "info");
                  appendDebugOutput(`Type mismatch: File is for ${result.savedType}, current playground is ${result.currentType}`, "info");

                  // Redirect to the correct playground
                  setTimeout(() => {
                    window.location.href = `/${result.savedType}?fileHandling=true`;
                  }, 1000);
                } else {
                  // Show error message
                  appendSimpleOutput("Sorry, there was a problem loading your VIPE project.", "error");
                  appendDebugOutput(`Load failed: ${result.error || "Unknown error"}`, "error");
                }
              }).catch(error => {
                console.error("Error loading file:", error);
                appendSimpleOutput("Sorry, there was a problem loading your VIPE project.", "error");
                appendDebugOutput(`Load failed: ${error.message}`, "error");
              });
            }, 1000); // Wait for playground to initialize
          } else {
            console.log("Pending file is too old, ignoring");
          }

          // Don't remove the pending file data yet, in case we need to redirect again
        } else {
          console.log("No pending file found in localStorage");
        }
      } catch (error) {
        console.error("Error checking for pending file:", error);
      }
    }, 1500); // Wait for DOM to be fully loaded
  }

  // Initialize file handling if supported
  if (fileHandlingSupport.supported) {
    initFileHandling({
      getPlayground: () => window.globalPlayground
    });
  }

  // Check for a pending file to load after configuration change
  checkPendingFileAfterConfigChange({
    getPlayground: () => window.globalPlayground
  });

  // Parse URL search parameters
  const urlParams = new URLSearchParams(window.location.search);
  const showBlocksTab = urlParams.get("blocks") !== "false"; // Default to true if not specified
  const showCodeTab = urlParams.get("code") !== "false"; // Default to true if not specified

  // Create a global variable to store the Monaco Editor instance
  window.monacoEditor = null;

  // Create a global function to get the current code
  // This will be used by the run button to get the code
  window.getEditorCode = function () {
    // First, ensure the code in the editor is up-to-date with the blocks
    if (window.updateGeneratedCode && typeof window.updateGeneratedCode === 'function') {
      window.updateGeneratedCode();
    }

    // Check if we have stored generated code
    if (window.generatedCode && window.generatedCode.trim() !== "") {
      console.log('Using stored generated code');
      const code = window.generatedCode;
      // Don't clear generatedCode here to allow it to be used again if needed
      return code;
    }

    // If Monaco Editor is available, get code from Monaco
    if (window.monacoEditor) {
      const code = getMonacoValue(window.monacoEditor);
      if (code && code.trim() !== "") {
        console.log('Using code from Monaco editor');
        return code;
      }
    }

    // If we still don't have code and Blockly is available, generate code from blocks
    if (window.Blockly && window.globalPlayground) {
      const activeWorkspaceName = window.globalPlayground.getActiveWorkspaceName();
      const activeWorkspace = window.globalPlayground.getWorkspace(activeWorkspaceName);

      if (activeWorkspace) {
        console.log('Generating code directly from blocks');

        // Initialize the JavaScript generator
        window.Blockly.JavaScript.init(activeWorkspace);

        let code = "";

        // Find all "when run clicked" blocks in this workspace
        const runEventBlocks = activeWorkspace.getBlocksByType("event_run_clicked");

        // Find all "key press" blocks in this workspace
        const keyPressBlocks = activeWorkspace.getBlocksByType("event_key_press");

        // Combine all event blocks
        const allEventBlocks = [...runEventBlocks, ...keyPressBlocks];

        if (allEventBlocks.length > 0) {
          // Generate code for each event block in this workspace
          for (let i = 0; i < allEventBlocks.length; i++) {
            const eventBlock = allEventBlocks[i];

            // Add a block identifier
            if (i > 0) {
              code += "\n\n";
            }

            // Get code from this event block
            code += window.Blockly.JavaScript.blockToCode(eventBlock);
          }

          if (code && code.trim() !== "") {
            console.log('Successfully generated code from blocks');
            // Store the generated code for future use
            window.generatedCode = code;
            return code;
          }
        }

        // If no event blocks, try generating code from all blocks
        code = window.Blockly.JavaScript.workspaceToCode(activeWorkspace);
        if (code && code.trim() !== "") {
          console.log('Generated code from all blocks');
          // Store the generated code for future use
          window.generatedCode = code;
          return code;
        }
      }
    }

    console.warn('Failed to generate any code');
    return null;
  };

  // Create a global function to update the editor code from blocks
  window.updateEditorCode = function () {
    if (window.updateGeneratedCode && typeof window.updateGeneratedCode === 'function') {
      window.updateGeneratedCode();
    }
  };

  // Handle tab switching
  const tabBlockly = document.getElementById("tab-blockly");
  const tabCode = document.getElementById("tab-code");
  const blocklyDiv = document.getElementById("blocklyDiv");
  const codeDiv = document.getElementById("codeDiv");

  // Configure tabs based on URL parameters
  if (tabBlockly && tabCode) {
    // If both tabs are disabled, default to showing blocks
    if (!showBlocksTab && !showCodeTab) {
      console.warn("Both blocks and code tabs are disabled. Defaulting to showing blocks tab.");
      tabBlockly.style.display = "";
      tabCode.style.display = "none";
      blocklyDiv.classList.remove("hidden");
      blocklyDiv.classList.add("block");
      codeDiv.classList.add("hidden");
      codeDiv.classList.remove("block");
    } else {
      // Configure blocks tab
      if (!showBlocksTab) {
        tabBlockly.style.display = "none";
        // If blocks tab is hidden and code tab is visible, activate code tab
        if (showCodeTab) {
          tabCode.classList.add("tab-active");
          codeDiv.classList.remove("hidden");
          codeDiv.classList.add("block");
          blocklyDiv.classList.add("hidden");
          blocklyDiv.classList.remove("block");
        }
      }

      // Configure code tab
      if (!showCodeTab) {
        tabCode.style.display = "none";
        // If code tab is hidden and blocks tab is visible, activate blocks tab
        if (showBlocksTab) {
          tabBlockly.classList.add("tab-active");
          blocklyDiv.classList.remove("hidden");
          blocklyDiv.classList.add("block");
          codeDiv.classList.add("hidden");
          codeDiv.classList.remove("block");
        }
      }
    }

    // We'll initialize Monaco Editor when the Code tab is first clicked
    // This prevents unnecessary initialization if the user never switches to the Code tab
  }

  // Function to update the generated code
  // This function is exported to window.updateGeneratedCode so it can be called from monaco-editor.js
  // eslint-disable-next-line no-unused-vars
  window.updateGeneratedCode = () => {
    // Log if Monaco Editor is not initialized, but continue to generate code
    // This allows us to generate code even if the editor is not visible
    if (!window.monacoEditor) {
      console.log('updateGeneratedCode: Monaco Editor not initialized, will generate code but not display it');
    }

    try {
      // Skip updating if Monaco Editor exists, code is editable and not empty (preserve user edits)
      if (window.monacoEditor && !showBlocksTab && getMonacoValue(window.monacoEditor).trim() !== "") {
        console.log('updateGeneratedCode: Preserving user edits in code tab');
        return;
      }
    } catch (error) {
      console.error("Error checking Monaco Editor value:", error);
      // Continue with code generation even if there's an error checking the editor value
    }

    // Get the active workspace name
    const activeWorkspace = window.globalPlayground ? window.globalPlayground.getActiveWorkspaceName() : 'none';
    console.log('updateGeneratedCode: Updating code display with active workspace:', activeWorkspace);

    // If we have a lastActiveEntityId, use that instead
    if (window.lastActiveEntityId && window.globalPlayground && window.globalPlayground.workspaces.has(window.lastActiveEntityId)) {
      const lastActiveWorkspace = window.lastActiveEntityId;
      if (lastActiveWorkspace !== activeWorkspace) {
        console.log(`Switching to last active workspace: ${lastActiveWorkspace}`);
        window.globalPlayground.setActiveWorkspace(lastActiveWorkspace);
      }
    }

    if (window.Blockly && window.globalPlayground) {
      let code = "// Run button clicked event.";
      let hasAnyRunEventBlock = false;

      // Get the active workspace name
      const activeWorkspaceName = window.lastActiveEntityId || window.globalPlayground.getActiveWorkspaceName();
      console.log(`Generating code for active workspace: ${activeWorkspaceName}`);

      // Get the active workspace
      const activeWorkspace = window.globalPlayground.getWorkspace(activeWorkspaceName);

      if (activeWorkspace) {
        // Find all "when run clicked" blocks in this workspace
        const runEventBlocks = activeWorkspace.getBlocksByType("event_run_clicked");

        // Find all "key press" blocks in this workspace
        const keyPressBlocks = activeWorkspace.getBlocksByType("event_key_press");

        // Combine all event blocks
        const allEventBlocks = [...runEventBlocks, ...keyPressBlocks];

        if (allEventBlocks.length > 0) {
          hasAnyRunEventBlock = true;

          // Initialize the JavaScript generator for this workspace
          window.Blockly.JavaScript.init(activeWorkspace);

          // Generate code for each event block in this workspace
          for (let i = 0; i < allEventBlocks.length; i++) {
            const eventBlock = allEventBlocks[i];

            // Add a block identifier
            if (allEventBlocks.length > 1) {
              code += `\n\n// Event block ${i + 1}:`;
            } else {
              code += "\n";
            }

            // Get code from this event block
            code += window.Blockly.JavaScript.blockToCode(eventBlock);
          }

          // Now find all top-level blocks that are not event blocks
          const allBlocks = activeWorkspace.getTopBlocks(true);
          const nonEventBlocks = allBlocks.filter(block =>
            block.type !== "event_run_clicked" &&
            block.type !== "event_key_press"
          );

          // If there are any non-event blocks, add them as commented code
          if (nonEventBlocks.length > 0) {
            code += "\n\n// Code outside of events (will not run automatically):";

            // Generate code for each non-event block
            for (let i = 0; i < nonEventBlocks.length; i++) {
              const block = nonEventBlocks[i];

              // Get code from this block
              let blockCode = window.Blockly.JavaScript.blockToCode(block);

              // Comment out each line
              if (blockCode && typeof blockCode === 'string') {
                blockCode = blockCode.split('\n').map(line =>
                  line.trim() ? '// ' + line : line
                ).join('\n');

                code += "\n" + blockCode;
              } else if (blockCode) {
                // If blockCode is not a string but is truthy, convert it to a string
                code += "\n// " + String(blockCode);
              }
            }
          }
        }
      } else {
        console.warn(`Active workspace not found: ${activeWorkspaceName}`);
      }

      // If no event blocks were found, add a message
      if (!hasAnyRunEventBlock) {
        code += `\n\n// No event blocks found in the ${activeWorkspaceName} workspace.`;
      }

      // Update Monaco Editor with the generated code if it exists
      if (window.monacoEditor) {
        try {
          setMonacoValue(window.monacoEditor, code);
          console.log('Code updated in Monaco Editor');
        } catch (error) {
          console.error("Error setting Monaco Editor value:", error);
        }
      } else {
        // Store the generated code for later use
        window.generatedCode = code;
        console.log('Code generated but Monaco Editor not available. Stored for later use.');
      }
    }
  };

  if (tabBlockly && tabCode && blocklyDiv && codeDiv) {

    // Switch to Blockly tab
    tabBlockly.addEventListener("click", () => {
      // Unfocus any active Blockly fields
      unfocusBlocklyFields();

      tabBlockly.classList.add("tab-active");
      tabCode.classList.remove("tab-active");
      blocklyDiv.classList.remove("hidden");
      blocklyDiv.classList.add("block");
      codeDiv.classList.add("hidden");
      codeDiv.classList.remove("block");

      // Just hide the editor when switching to Blockly tab
      // No need to dispose it, we'll reuse it when switching back

      // Check if we need to restore the active entity's workspace
      if (window.globalPlayground && window.lastActiveEntityId) {
        // Use setTimeout to ensure the DOM has updated
        setTimeout(() => {
          // Make sure the active entity's workspace is visible
          const activeEntityId = window.lastActiveEntityId;
          if (activeEntityId) {
            // Set the active workspace
            window.globalPlayground.setActiveWorkspace(activeEntityId);

            // Show the correct workspace div
            document.querySelectorAll('[id^="blocklyDiv-"]').forEach(div => {
              div.style.display = div.id === `blocklyDiv-${activeEntityId}` ? 'block' : 'none';
            });

            // Resize the workspace
            if (window.Blockly) {
              const workspace = window.globalPlayground.getWorkspace(activeEntityId);
              if (workspace) {
                window.Blockly.svgResize(workspace);
                workspace.render();
              }
            }
          }
        }, 100);
      }
    });

    // Switch to Code tab
    tabCode.addEventListener("click", () => {
      // Unfocus any active Blockly fields
      unfocusBlocklyFields();

      tabCode.classList.add("tab-active");
      tabBlockly.classList.remove("tab-active");
      codeDiv.classList.remove("hidden");
      codeDiv.classList.add("block");
      blocklyDiv.classList.add("hidden");
      blocklyDiv.classList.remove("block");

      // We'll let the monaco-editor.js script handle the initialization
      // Just make sure the editor is visible and properly sized
      if (window.monacoEditor) {
        // Store the current active workspace name for reference
        if (window.globalPlayground) {
          window.lastActiveEntityId = window.globalPlayground.getActiveWorkspaceName();
          console.log('Code tab clicked, active workspace:', window.lastActiveEntityId);
        }

        // Update the generated code when switching to the Code tab
        if (window.updateGeneratedCode && typeof window.updateGeneratedCode === 'function') {
          // Clear the Monaco editor first to ensure we don't see old code
          try {
            // If we have stored generated code, use it instead of clearing
            if (window.generatedCode) {
              console.log('Using previously generated code');
              setMonacoValue(window.monacoEditor, window.generatedCode);
              window.generatedCode = null; // Clear the stored code after using it
            } else {
              setMonacoValue(window.monacoEditor, "");
            }
          } catch (error) {
            console.error("Error setting Monaco Editor value:", error);
          }

          // Use setTimeout to ensure the DOM has updated before updating code
          setTimeout(() => {
            // Check if the isUpdatingCode flag exists and is not set
            if (!window.isUpdatingCode) {
              // Set a global flag to prevent recursive calls
              window.isUpdatingCode = true;
              window.updateGeneratedCode();
              // Reset the flag after a short delay
              setTimeout(() => {
                window.isUpdatingCode = false;
              }, 200);
            } else {
              console.log('Already updating code, skipping recursive call');
            }
          }, 100);
        }
        try {
          // Resize the editor to fit its container with a slight delay
          setTimeout(function () {
            // Update the editor layout
            window.monacoEditor.layout();

            // Apply another layout update after a short delay to handle any delayed rendering
            setTimeout(function () {
              window.monacoEditor.layout();
            }, 100);
          }, 50);

          // Update editor read-only state based on playground configuration
          if (window.monacoEditor.updateOptions && window.globalPlayground) {
            const config = window.globalPlayground.getConfig();
            window.monacoEditor.updateOptions({ readOnly: config.readOnlyCode === true });
          }

          // No editor messages - keep Monaco container clean
        } catch (error) {
          console.error("Error updating Monaco Editor:", error);
        }
      }


    });

    // Code is automatically updated when switching to the Code tab
  }

  // Handle output tabs switching
  const tabSimpleOutput = document.getElementById("tab-simple-output");
  const tabDebugOutput = document.getElementById("tab-debug-output");
  const simpleOutputPane = document.getElementById("simple-output-pane");
  const debugOutputPane = document.getElementById("debug-output-pane");
  const clearOutputButton = document.getElementById("clearOutputButton");

  if (tabSimpleOutput && tabDebugOutput && simpleOutputPane && debugOutputPane) {
    // Switch to Simple output tab
    tabSimpleOutput.addEventListener("click", () => {
      tabSimpleOutput.classList.add("tab-active");
      tabDebugOutput.classList.remove("tab-active");
      simpleOutputPane.classList.remove("hidden");
      simpleOutputPane.classList.add("block");
      debugOutputPane.classList.add("hidden");
      debugOutputPane.classList.remove("block");
    });

    // Switch to Debug output tab
    tabDebugOutput.addEventListener("click", () => {
      tabDebugOutput.classList.add("tab-active");
      tabSimpleOutput.classList.remove("tab-active");
      debugOutputPane.classList.remove("hidden");
      debugOutputPane.classList.add("block");
      simpleOutputPane.classList.add("hidden");
      simpleOutputPane.classList.remove("block");
    });
  }

  // Handle clear output button
  if (clearOutputButton) {
    clearOutputButton.addEventListener("click", () => {
      clearOutput();
      appendSimpleOutput("Output cleared manually.", "warning");
      appendDebugOutput("Output cleared by user.", "warning");
    });
  }

  // Add change listener to update code when blocks change
  setTimeout(() => {
    // Add change listener to all Blockly workspaces
    if (window.Blockly && window.globalPlayground && window.globalPlayground.workspaces) {
      // Iterate through all workspaces
      const workspacesMap = window.globalPlayground.workspaces;
      for (const workspace of workspacesMap.values()) {
        workspace.addChangeListener((event) => {
          // Only update on UI events (block added, moved, deleted, etc.)
          if (event.type === Blockly.Events.BLOCK_CREATE ||
            event.type === Blockly.Events.BLOCK_DELETE ||
            event.type === Blockly.Events.BLOCK_CHANGE ||
            event.type === Blockly.Events.BLOCK_MOVE) {
            if (window.updateGeneratedCode && typeof window.updateGeneratedCode === 'function') {
              window.updateGeneratedCode();
            }
          }
        });
      }

      // Use a global flag to prevent recursive calls
      window.isUpdatingCode = window.isUpdatingCode || false;

      // Also listen for workspace changes to update code when a new workspace is added or active workspace changes
      window.globalPlayground.addEventListener('workspace_changed', (event) => {
        const workspaceName = event.detail ? event.detail.workspaceName : 'unknown';
        console.log('Workspace changed event:', workspaceName);

        // Store the active workspace name for reference
        window.lastActiveEntityId = workspaceName;

        // Get the code tab element
        const tabCode = document.getElementById("tab-code");

        // Only update code if we're on the code tab
        const isCodeTabActive = tabCode && tabCode.classList.contains("tab-active");

        if (!isCodeTabActive) {
          console.log('Not on code tab, skipping code update');
          return;
        }

        // Prevent recursive calls
        if (window.isUpdatingCode) {
          console.log('Already updating code, skipping recursive call');
          return;
        }

        if (window.updateGeneratedCode && typeof window.updateGeneratedCode === 'function') {
          // Set flag to prevent recursive calls
          window.isUpdatingCode = true;

          // Clear the Monaco editor first to ensure we don't see old code
          if (window.monacoEditor) {
            try {
              setMonacoValue(window.monacoEditor, "");
            } catch (error) {
              console.error("Error clearing Monaco Editor:", error);
            }
          }

          // Use setTimeout to ensure the workspace is fully switched before updating code
          setTimeout(() => {
            window.updateGeneratedCode();
            // Reset flag after update is complete
            setTimeout(() => {
              window.isUpdatingCode = false;
            }, 200);
          }, 100);
        }
      });
    }

    // Fix for Blockly toolbox scrollbar issues
    const fixToolboxScrollbar = () => {
      const toolboxDiv = document.querySelector(".blocklyToolboxDiv");
      if (toolboxDiv) {
        toolboxDiv.style.overflow = "hidden";
        setTimeout(() => {
          toolboxDiv.style.overflow = "auto";
        }, 50);
      }
    };

    // Set up observer for Blockly workspace changes
    const observer = new MutationObserver(fixToolboxScrollbar);
    const blocklyDiv = document.getElementById("blocklyDiv");

    if (blocklyDiv) {
      observer.observe(blocklyDiv, {
        childList: true,
        subtree: true
      });
    }

    // Add click event listener for category buttons
    document.addEventListener("click", (e) => {
      if (e.target.closest(".blocklyTreeRow")) {
        fixToolboxScrollbar();
      }
    });

    // Initial fix
    fixToolboxScrollbar();

    // Setup import/export functionality
    setupImportExport();
  }, 1000); // Wait for Blockly to initialize

  // Function to handle import and export of Blockly workspace
  function setupImportExport() {
    const importButton = document.getElementById("importButton");
    const exportButton = document.getElementById("exportButton");

    // Create a LoadSaveManager instance
    const loadSaveManager = new LoadSaveManager({
      getWorkspaces: () => {
        // Return all workspaces from the playground
        return window.globalPlayground.workspaces;
      },
      getActiveWorkspaceName: () => window.globalPlayground.getActiveWorkspaceName(),
      setActiveWorkspace: (name) => window.globalPlayground.setActiveWorkspace(name),
      getPlaygroundType: () => {
        // Get the current playground type from the window.globalPlayground.type property
        if (window.globalPlayground && window.globalPlayground.type) {
          return window.globalPlayground.type;
        }

        // If we can't determine the type from the playground object,
        // try to determine it from the URL or other context
        const url = window.location.href;
        if (url.includes('positional.html') || url.includes('positional')) {
          return 'positional';
        } else if (url.includes('grid.html') || url.includes('grid')) {
          return 'grid';
        }

        // Default to grid if we can't determine the type
        return 'grid';
      },
      getEntities: () => window.globalPlayground.getEntities ? window.globalPlayground.getEntities() : [],
      setEntities: (entities) => {
        if (window.globalPlayground.setEntities && typeof window.globalPlayground.setEntities === 'function') {
          window.globalPlayground.setEntities(entities);
        }
      },
      getPlaygroundState: () => {
        if (window.globalPlayground.getState && typeof window.globalPlayground.getState === 'function') {
          return window.globalPlayground.getState();
        }
        return null;
      },
      setPlaygroundState: (state) => {
        if (window.globalPlayground.setState && typeof window.globalPlayground.setState === 'function') {
          window.globalPlayground.setState(state);
        }
      }
    });

    if (importButton && exportButton) {
      // Handle load button click
      importButton.addEventListener("click", async () => {
        try {
          const result = await loadSaveManager.showLoadDialog();
          if (result.success) {
            // Check if there was a playground type mismatch
            if (result.typeMismatch) {
              // Show error message for type mismatch
              appendSimpleOutput(`Project loading failed: This project requires a different playground type.`, "error");
              appendDebugOutput(`Load failed: ${result.savedType} project cannot be loaded in ${result.currentType} environment.`, "error");
            } else {
              // Show standard success message
              appendSimpleOutput("Your VIPE project has been loaded successfully!", "success");
              appendDebugOutput("Load successful: VIPE project loaded from .vipar file.", "success");
            }
          }
        } catch (error) {
          console.error(error);
          // Show error message in both output panes
          appendSimpleOutput("Sorry, there was a problem loading your VIPE project.", "error");
          appendDebugOutput(`Load failed: ${error.message}`, "error");
        }
      });

      // Handle save button click
      exportButton.addEventListener("click", () => {
        try {
          loadSaveManager.saveToFile();

          // Show success message in both output panes
          appendSimpleOutput("Your VIPE project has been saved to a file!", "success");
          appendDebugOutput("Save successful: VIPE project saved as .vipar file.", "success");
        } catch (error) {
          console.error(error);
          // Show error message in both output panes
          appendSimpleOutput("Sorry, there was a problem saving your VIPE project.", "error");
          appendDebugOutput(`Save failed: ${error.message}`, "error");
        }
      });
    }
  }
});
