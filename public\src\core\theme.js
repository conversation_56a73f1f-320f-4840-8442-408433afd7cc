// Define a custom theme with pastel colors
export const theme = Blockly.Theme.defineTheme("pastel", {
  // Set font size to 10pt
  fontStyle: {
    size: 10
  },
  // Define block styles with pastel colors
  blockStyles: {
    // Output blocks (Light grayscale color)
    output_blocks: {
      colourPrimary: "#9E9E9E",    // Medium gray
      colourSecondary: "#E0E0E0",  // Light gray
      colorTertiary: "#757575"     // Darker gray
    },
    // Events blocks (Pastel Orange)
    event_blocks: {
      colourPrimary: "#FFCC80",
      colourSecondary: "#FFE0B2",
      colorTertiary: "#FFB74D"
    },
    // Motion blocks (Pastel Blue)
    motion_blocks: {
      colourPrimary: "#90CAF9",
      colourSecondary: "#BBDEFB",
      colorTertiary: "#64B5F6"
    },
    // Logic blocks (Light purple/lavender)
    logic_blocks: {
      colourPrimary: "#D4C5F9",
      colourSecondary: "#E2D9FA",
      colorTertiary: "#C2AFF8"
    },
    // Loop blocks (Light green)
    loop_blocks: {
      colourPrimary: "#A5D6A7",
      colourSecondary: "#C8E6C9",
      colorTertiary: "#81C784"
    },
    // Math blocks (Lighter blue to match the pastel scheme)
    math_blocks: {
      colourPrimary: "#90B3D9",
      colourSecondary: "#B3D1E8",
      colorTertiary: "#6D9AC9"
    },
    // Text blocks (Darker yellow for better visibility)
    text_blocks: {
      colourPrimary: "#E6B800",
      colourSecondary: "#FFCC00",
      colorTertiary: "#CC9900"
    },
    // White text blocks for output messages
    output_text_blocks: {
      colourPrimary: "#EEEEEE",    // Light gray/white
      colourSecondary: "#F5F5F5",  // Lighter gray/white
      colorTertiary: "#E0E0E0"     // Slightly darker gray/white
    },
    // List blocks (Coral/Orange)
    list_blocks: {
      colourPrimary: "#ffb4a2",      // Main coral-orange
      colourSecondary: "#ffd0c4",    // Lighter coral-orange
      colorTertiary: "#ff9b84"       // Slightly darker coral-orange
    },
    // Variable blocks (Light pink/salmon)
    variable_blocks: {
      colourPrimary: "#F48FB1",
      colourSecondary: "#F8BBD0",
      colorTertiary: "#F06292"
    },
    variable_dynamic_blocks: {
      colourPrimary: "#F48FB1",
      colourSecondary: "#F8BBD0",
      colorTertiary: "#F06292"
    },
    // Procedure blocks (Pastel Indigo)
    procedure_blocks: {
      colourPrimary: "#9FA8DA",
      colourSecondary: "#C5CAE9",
      colorTertiary: "#7986CB"
    }
  },
  // Define category styles to match block colors
  categoryStyles: {
    output_category: { colour: "#9E9E9E" },
    events_category: { colour: "#FFCC80" },
    motion_category: { colour: "#90CAF9" },
    logic_category: { colour: "#D4C5F9" }, // Light purple/lavender
    loops_category: { colour: "#A5D6A7" }, // Light green
    math_category: { colour: "#90B3D9" }, // Lighter blue to match the pastel scheme
    text_category: { colour: "#E6B800" }, // Darker yellow
    lists_category: { colour: "#ffb4a2" },    // Updated color
    variables_category: { colour: "#F48FB1" }, // Light pink/salmon
    procedures_category: { colour: "#9FA8DA" }
  },
  // Component styles
  componentStyles: {
    workspaceBackgroundColour: "#FAFAFA",
    toolboxBackgroundColour: "#F5F5F5",
    toolboxForegroundColour: "#424242",
    flyoutBackgroundColour: "#F9F9F9",
    flyoutForegroundColour: "#424242",
    flyoutOpacity: 0.9,
    scrollbarColour: "#BDBDBD",
    scrollbarOpacity: 0.7
  }
});
