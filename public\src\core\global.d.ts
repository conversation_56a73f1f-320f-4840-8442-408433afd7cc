type BlocklyType = typeof import("blockly");
type JavascriptGenerator = import("blockly/javascript").JavascriptGenerator;
type JavascriptOrder = typeof import("blockly/javascript").Order;

type IStandaloneCodeEditor = import("monaco-editor").editor.IStandaloneCodeEditor;

declare const Blockly: Omit<BlocklyType, "JavaScript"> & {
  JavaScript: JavascriptGenerator & { Order: JavascriptOrder };
};
declare const javascript: JavascriptGenerator & { Order: JavascriptOrder };

// based on ../interpreter/interpreter.js
declare const Interpreter: {
  new (
    code: string | [string],
    initAPI?: (
      interpreter: InterpreterInstance,
      globalObject: typeof InterpreterGlobalScopeObject
    ) => void
  ): InterpreterInstance;
  prototype: InterpreterInstance;
};
declare interface InterpreterInstance {
  run(): void;
  step(): boolean;
  getStateStack(): object[];
  createNativeFunction<T extends Function>(fn: T): T;
  nativeToPseudo(value: any): any;
  hasNextStep(): boolean;
  setProperty(scope: any, name: string, value: any): void;
  setProperty(name: string, value: any): void;
  getProperty(name: string): any;
  getPropertyValue(name: string): any;
  getGlobalScope(): typeof InterpreterGlobalScope;
  getScope(): object;
  setScope(scope: object): void;
  nextStep(): void;
  isRunning(): boolean;
  setAsync(async: boolean): void;
  getAsync(): boolean;
}
declare const InterpreterGlobalScope: {
  [key: string]: any;
};
declare const InterpreterGlobalScopeObject: InterpreterInstance;

declare const monacoEditor: IStandaloneCodeEditor;