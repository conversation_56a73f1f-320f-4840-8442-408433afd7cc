// @ts-check

import { appendDebugOutput, appendSimpleOutput, clearOutput } from "../utils/output.js";
import { IconField } from "./custom_fields.js";
import PlaygroundRenderer from "./playground-renderer.js";
import * as UI from "../utils/ui.js";
import Locked<PERSON>eyMap from "../utils/locked-key-map.js";

// Event types
/** @enum {"execution_complete" | "execution_start" | "execution_error" | "workspace_changed"} */
const EVENTS = {
  EXECUTION_COMPLETE: /** @type {const} */ ("execution_complete"),
  EXECUTION_START:  /** @type {const} */ ("execution_start"),
  EXECUTION_ERROR:  /** @type {const} */ ("execution_error"),
  WORKSPACE_CHANGED: /** @type {const} */ ("workspace_changed")
};

/** @template {string} [K=string] */
export default class Playground extends EventTarget {
  // Delay between interpreter steps (in milliseconds)
  static #INTERPRETER_DELAY = 10;
  #workspaces;
  #playgroundRenderer;
  /** @type {string} */
  #activeWorkspaceName = "default";
  /** @type {Map<string, { interpreter: InterpreterInstance; fn: { node: object }; data?: any }[]>} */
  #eventHandlers = new Map();
  #interpreterInitiator;
  /** @type {string | null} */
  lastPressedKey = null;
  /** @type {string} */
  type = "unknown";

  /** @type {Object} */
  #config = {
    showBlocksTab: true,
    showCodeTab: true,
    defaultTab: "blocks",
    readOnlyCode: true
  };

  // Expose event types as static property
  static get EVENTS() {
    return EVENTS;
  }

  /**
   * Get the current configuration
   * @returns {Object} The current configuration
   */
  getConfig() {
    return { ...this.#config };
  }

  /**
   * Set the configuration
   * @param {Object} [config] - The configuration to set
   */
  setConfig(config) {
    // Parse URL parameters to get the current configuration
    const urlParams = new URLSearchParams(window.location.search);

    const showBlocksTab = urlParams.get("blocks") !== "false"; // Default to true if not specified
    const showCodeTab = urlParams.get("code") !== "false"; // Default to true if not specified

    // Determine if code should be read-only based on URL parameters
    // If blocks=false (blocks disabled), code should be editable
    // If code=true is explicitly set, code should be editable
    // Otherwise, code is read-only when blocks are available (default behavior)
    let readOnlyCode = this.#config.readOnlyCode;

    if (urlParams.get("blocks") === "false") {
      // Blocks are disabled, so code should be editable
      readOnlyCode = false;
    } else if (urlParams.get("code") === "true") {
      // Code is explicitly enabled, so it should be editable
      readOnlyCode = false;
    } else if (showBlocksTab) {
      // Default behavior: code is read-only when blocks are available
      readOnlyCode = true;
    }

    // Determine default tab based on URL parameters
    let defaultTab = this.#config.defaultTab;
    if (urlParams.get("blocks") === "false") {
      // If blocks are disabled, default to code tab
      defaultTab = "code";
    } else if (urlParams.get("code") === "true") {
      // If code is explicitly enabled, default to code tab
      defaultTab = "code";
    }

    // Update configuration with URL parameters
    this.#config = {
      showBlocksTab: showBlocksTab,
      showCodeTab: showCodeTab,
      defaultTab: defaultTab,
      readOnlyCode: readOnlyCode
    };

    // Override with provided config
    if (config) {
      this.#config = { ...this.#config, ...config };
    }

    // Apply configuration to UI
    this.#applyConfig();

    return this.#config;
  }

  /**
   * Apply the current configuration to the UI
   */
  #applyConfig() {
    const config = this.#config;
    const tabBlockly = document.getElementById("tab-blockly");
    const tabCode = document.getElementById("tab-code");
    const blocklyDiv = document.getElementById("blocklyDiv");
    const codeDiv = document.getElementById("codeDiv");

    if (tabBlockly && tabCode && blocklyDiv && codeDiv) {
      // Configure blocks tab
      if (config.showBlocksTab === false) {
        tabBlockly.style.display = "none";
        // If blocks tab is hidden and code tab is visible, activate code tab
        if (config.showCodeTab !== false) {
          tabCode.classList.add("tab-active");
          codeDiv.classList.remove("hidden");
          codeDiv.classList.add("block");
          blocklyDiv.classList.add("hidden");
          blocklyDiv.classList.remove("block");
        }
      } else {
        tabBlockly.style.display = "";
      }

      // Configure code tab
      if (config.showCodeTab === false) {
        tabCode.style.display = "none";
        // If code tab is hidden and blocks tab is visible, activate blocks tab
        if (config.showBlocksTab !== false) {
          tabBlockly.classList.add("tab-active");
          blocklyDiv.classList.remove("hidden");
          blocklyDiv.classList.add("block");
          codeDiv.classList.add("hidden");
          codeDiv.classList.remove("block");
        }
      } else {
        tabCode.style.display = "";
      }

      // Set default tab
      if (config.defaultTab === "code" && config.showCodeTab !== false) {
        tabCode.classList.add("tab-active");
        tabBlockly.classList.remove("tab-active");
        codeDiv.classList.remove("hidden");
        codeDiv.classList.add("block");
        blocklyDiv.classList.add("hidden");
        blocklyDiv.classList.remove("block");
      } else if (config.defaultTab === "blocks" && config.showBlocksTab !== false) {
        tabBlockly.classList.add("tab-active");
        tabCode.classList.remove("tab-active");
        blocklyDiv.classList.remove("hidden");
        blocklyDiv.classList.add("block");
        codeDiv.classList.add("hidden");
        codeDiv.classList.remove("block");
      }

      // Configure code editor read-only state
      // @ts-ignore - monacoEditor is added dynamically in main.js
      if (window.monacoEditor && typeof window.monacoEditor.updateOptions === 'function') {
        // @ts-ignore - monacoEditor is added dynamically in main.js
        window.monacoEditor.updateOptions({ readOnly: config.readOnlyCode === true });
      }
    }
  }

  /**
   * @param {{ [ _ in K | "default" ]: InstanceType<Blockly["WorkspaceSvg"]> }} workspaces
   * @param {PlaygroundRenderer} playgroundRenderer
   * @param {(code: string, initAPI: (interpreter: InterpreterInstance, globalObject: InterpreterGlobalScopeObject) => void, workspaceName?: string) => InterpreterInstance} interpreterInitiator
  */
  constructor(workspaces, playgroundRenderer, interpreterInitiator) {
    super();
    // Register the field with Blockly
    Blockly.fieldRegistry.register("field_icon", IconField);
    this.#workspaces = new LockedKeyMap(
      [
        /** @type {const} */ ([ "default", workspaces.default ])
      ],
      /** @type {[ K, InstanceType<Blockly["WorkspaceSvg"]> ][]} */ (Object.entries(workspaces).filter(entry => entry[0] !== "default")),
    );
    this.#playgroundRenderer = playgroundRenderer;
    this.#interpreterInitiator = interpreterInitiator;

    // Initialize configuration from URL parameters
    this.setConfig();
  }

  /**
   * Get the active workspace
   * @returns {InstanceType<Blockly["WorkspaceSvg"]>} The active workspace
   */
  get workspace() {
    // If an active workspace is set, return that one, otherwise return the default
    // @ts-ignore - TypeScript doesn't understand that this will always return a valid workspace
    return this.#workspaces.get(this.#activeWorkspaceName || "default");
  }

  get workspaces() {
    return this.#workspaces;
  }

  /**
   * Get a workspace by name
   * @template {string} T
   * @param {T} name - The name of the workspace
   * @returns {InstanceType<Blockly["WorkspaceSvg"]> | undefined} The workspace or undefined if not found
   */
  getWorkspace(name) {
    // @ts-ignore - This is a valid operation but TypeScript doesn't like it
    return this.#workspaces.get(name);
  }

  /**
   * Set the active workspace
   * @param {string} name - The name of the workspace to set as active
   * @returns {boolean} Whether the workspace was found and set as active
   */
  setActiveWorkspace(name) {
    const workspace = this.getWorkspace(name);
    if (!workspace) return false;

    // Store the current active workspace name
    this.#activeWorkspaceName = name;

    // Dispatch an event to notify that the active workspace has changed
    this.dispatchEvent(new CustomEvent(Playground.EVENTS.WORKSPACE_CHANGED, {
      detail: { workspaceName: name }
    }));

    return true;
  }

  /**
   * Get the name of the active workspace
   * @returns {string} The name of the active workspace
   */
  getActiveWorkspaceName() {
    return this.#activeWorkspaceName || "default";
  }

  get playgroundRenderer() {
    return this.#playgroundRenderer;
  }

  init() {
    const buttons = {
      run: UI.getElementByIdAs("runButton", HTMLButtonElement),
      stop: UI.getElementByIdAs("stopButton", HTMLButtonElement),
      reset: UI.getElementByIdAs("resetButton", HTMLButtonElement)
    };

    // Set up button event listeners
    buttons.run?.addEventListener("click", async () => {
      clearOutput();

      // Stop any currently running code first
      this.stopCodeExecution();

      // Reset the playground once before running any code
      this.#playgroundRenderer.reset();
      this.#playgroundRenderer.render();
      buttons.stop.disabled = false;
      buttons.run.disabled = true;
      buttons.reset.disabled = true;

      // Log that we're starting execution
      appendDebugOutput("Starting code execution", "info");
      appendSimpleOutput("Your program is running...", "info");

      try {
        // Update the editor code from blocks
        // @ts-ignore - updateEditorCode is added dynamically in main.js
        if (window.updateEditorCode && typeof window.updateEditorCode === 'function') {
          // @ts-ignore - updateEditorCode is added dynamically in main.js
          window.updateEditorCode();
        }

        // Get code from the editor
        // @ts-ignore - getEditorCode is added dynamically in main.js
        if (window.getEditorCode && typeof window.getEditorCode === 'function') {
          // @ts-ignore - getEditorCode is added dynamically in main.js
          const editorCode = window.getEditorCode();
          if (editorCode) {
            // Clear existing event handlers to ensure we're using the latest code
            this.#eventHandlers.clear();

            // Run the code from the editor
            await this.runCode(editorCode);

            // Dispatch the RUN_CLICKED event to execute event handlers
            await this.dispatchInterpreterEvent("RUN_CLICKED");
          } else {
            appendSimpleOutput("No code found in the editor. Please add some code to get started!", "info");
          }
        } else {
          appendSimpleOutput("Code editor not available. Please refresh the page and try again.", "error");
        }
      } catch (error) {
        appendSimpleOutput(`Error in your code: ${error.message}`, "error");
        appendDebugOutput(`Error in editor code: ${error.message}`, "error");
      }

      // Re-enable buttons
      buttons.run.disabled = false;
      buttons.stop.disabled = true;
      buttons.reset.disabled = false;
    });

    buttons.stop?.addEventListener("click", () => {
      this.#playgroundRenderer.reset();
      this.stopCodeExecution();
      appendSimpleOutput("Program stopped.", "warning");

      buttons.run.disabled = false;
      buttons.stop.disabled = true;
      buttons.reset.disabled = false;
    });

    buttons.reset?.addEventListener("click", () => {
      // Stop any running code first
      this.stopCodeExecution();

      // Clear event handlers to prevent them from running after reset
      this.clearEventHandlers();

      // Reset the playground
      this.#playgroundRenderer.reset();
      this.#playgroundRenderer.render();

      // Clear output
      clearOutput();
      appendSimpleOutput("Game reset.", "warning");
      appendDebugOutput("Event handlers cleared", "info");
    });

    // Add resize handler to ensure Blockly takes full height
    window.addEventListener("resize", () => {
      for(const workspace of this.#workspaces.values()) {
        Blockly.svgResize(workspace);
      }
    });

    // Initial resize for all workspaces
    for(const workspace of this.#workspaces.values()) {
      Blockly.svgResize(workspace);
    }

    // Add key press event listener to the document
    document.addEventListener('keydown', async (event) => {
      // Get the key that was pressed
      const key = event.key;
      appendDebugOutput(`Key pressed: ${key}`, "info");

      try {
        // Dispatch the KEY_PRESS event to all registered handlers
        await this.dispatchInterpreterEvent("KEY_PRESS", { key: event.key, code: event.code });
      } catch (error) {
        appendSimpleOutput(`Error handling key press: ${error.message}`, "error");
      }
    });

    // Add click event listener to execute blocks when clicked for all workspaces
    for(const [workspaceName, workspace] of this.#workspaces.entries()) {
      workspace.addChangeListener(async event => {
        // Only handle click events
        if (event.type !== Blockly.Events.CLICK) return;

        // Get the clicked block
        // @ts-ignore - blockId is a valid property on Blockly.Events.Click
        const blockId = event.blockId;
        if (!blockId) return;

        const block = /** @type {(Omit<InstanceType<Blockly["BlockSvg"]>, "data"> & { data: { executable?: boolean } }) | null} */
          (workspace.getBlockById(blockId));
        if (block === null) return;

        // Execute the single block
        await this.executeSingleBlock(block, workspace, workspaceName);
      });
    }

  }

  /**
   * Dispatch an event to all registered handlers of that type
   * @param {string} eventType - The type of event to dispatch
   * @param {any} [data] - Optional data to pass to the event handlers
   * @returns {Promise<void>}
   */
  async dispatchInterpreterEvent(eventType, data = {}) {
    // Get all handlers for this event type
    const handlers = this.#eventHandlers.get(eventType);

    // If no handlers, return early
    if (handlers === undefined || handlers.length === 0) {
      appendDebugOutput(`No handlers found for event: ${eventType}`, "info");
      return;
    }

    // Execute handlers one at a time to avoid conflicts
    for (const handler of handlers) {
      try {
        // Get the interpreter instance
        const interpreter = handler.interpreter;

        // Get the global scope
        const globalScope = interpreter.getGlobalScope();

        // Create the event object
        const event = {
          type: "Literal",
          value: interpreter.nativeToPseudo(data)
        };

        // Push the function call onto the interpreter's state stack
        interpreter.getStateStack().push({
          node: {
            type: "CallExpression",
            callee: handler.fn.node,
            arguments: [ event ],
          },
          done: false,
          scope: globalScope,
          thisExpression: globalScope,
        });

        // Run the interpreter
        await this.#runInterpreterToCompletion(interpreter, `${eventType} handler`);
      } catch (error) {
        appendSimpleOutput(`Error in ${eventType} handler: ${error.message}`, "error");
      }
    }
  }

  /**
   * Run code step by step with visualization
   * @param {string} code - The JavaScript code to run
   * @param {string} [workspaceName] - The name of the workspace (entity ID)
   * @returns {Promise<number>} The number of steps executed
   */
  async runCode(code, workspaceName) {
    // Log the code being executed
    appendDebugOutput(`Running code for workspace ${workspaceName || 'default'}`, "info");

    // Dispatch execution start event
    this.dispatchEvent(
      new CustomEvent(
        Playground.EVENTS.EXECUTION_START,
        { detail: { code, workspaceName } }
      )
    );

    try {
      // Create a new interpreter instance
      const interpreter = this.#interpreterInitiator(code, (interpreter, globalObject) => {
        // Add the 'when' function for registering event handlers
        /**
         * @param {string} type - The event type (e.g., "RUN_CLICKED", "KEY_PRESS")
         * @param {{ node: any }} handler - The handler function
         * @param {any} [options] - Additional options (e.g., key for KEY_PRESS events)
         */
        const when = (type, handler, options) => {
          // Initialize the event handlers map for this type if it doesn't exist
          if (!this.#eventHandlers.has(type)) {
            this.#eventHandlers.set(type, []);
          }

          // Add the handler to the event handlers map
          this.#eventHandlers.get(type)?.push({ interpreter, fn: handler, data: options });

          // Log registration of event handlers
          if (type === "KEY_PRESS") {
            appendDebugOutput(`Registered KEY_PRESS handler for key: ${ options }`, "info");
          } else {
            appendDebugOutput(`Registered ${type} handler`, "info");
          }
        };

        // Expose API functions to the interpreter
        interpreter.setProperty(globalObject, "when", interpreter.createNativeFunction(when));
        interpreter.setProperty(globalObject, "appendOutput", interpreter.createNativeFunction(Playground.#appendOutput));

        // Add a getCurrentEntityId function to the interpreter
        const getCurrentEntityIdFn = () => workspaceName ?? this.#activeWorkspaceName ?? "default";
        interpreter.setProperty(globalObject, "getCurrentEntityId", interpreter.createNativeFunction(getCurrentEntityIdFn));
      });

      // Run the interpreter
      const stepCount = await this.#runInterpreterToCompletion(interpreter, workspaceName || 'default');

      // Dispatch execution complete event
      this.dispatchEvent(
        new CustomEvent(
          Playground.EVENTS.EXECUTION_COMPLETE,
          { detail: { stepCount, workspaceName } }
        )
      );

      return stepCount;
    } catch (error) {
      // Log the error
      appendSimpleOutput(`Error in code: ${error.message}`, "error");

      // Dispatch execution error event
      this.dispatchEvent(
        new CustomEvent(
          Playground.EVENTS.EXECUTION_ERROR,
          { detail: { error } }
        )
      );

      throw error;
    }
  }

  /**
   * Stop all running code execution
   */
  stopCodeExecution() {
    // Clear any block highlighting
    this.clearBlockHighlights();

    // Mark all executions as stopped
    for (const key in this) {
      if (key.startsWith('executionId-')) {
        // Mark this execution as stopped
        // @ts-ignore - We know this is a boolean property we added dynamically
        this[key] = false;

        // Clean up
        delete this[key];
      }
    }

    // Clear event handlers
    this.clearEventHandlers();

    appendDebugOutput("Code execution stopped", "warning");
  }

  /**
   * Clear all registered event handlers
   */
  clearEventHandlers() {
    this.#eventHandlers.clear();
    appendDebugOutput("Event handlers cleared", "info");
  }

  /**
   * Run an interpreter to completion
   * @param {InterpreterInstance} interpreter - The interpreter to run
   * @param {string} [context] - Optional context for logging
   * @returns {Promise<number>} The number of steps executed
   */
  async #runInterpreterToCompletion(interpreter, context = 'unknown') {
    let stepCount = 0;

    // Create a unique ID for this execution
    const executionId = `${context}-${Date.now()}`;

    // Store the execution ID to help with debugging
    this[`executionId-${executionId}`] = true;

    try {
      // Run the interpreter until it completes
      while (interpreter.step()) {
        // Check if execution was stopped
        if (!this[`executionId-${executionId}`]) {
          return stepCount;
        }

        stepCount++;

        // Add a small delay between steps to prevent browser freezing
        await new Promise(resolve => setTimeout(resolve, Playground.#INTERPRETER_DELAY));
      }

      return stepCount;
    } catch (error) {
      appendSimpleOutput(`Runtime error: ${error.message}`, "error");
      throw error;
    } finally {
      // Clean up the execution ID
      delete this[`executionId-${executionId}`];
    }
  }

  /**
   * Execute a single block
   * @param {any} block - The block to execute
   * @param {InstanceType<Blockly["WorkspaceSvg"]>} workspace - The workspace containing the block
   * @param {string} workspaceName - The name of the workspace
   * @returns {Promise<void>}
   */
  async executeSingleBlock(block, workspace, workspaceName) {
    // Don't execute event blocks when clicked
    if (block.type === "event_run_clicked" || block.type === "event_key_press") return;

    // Don't execute blocks that are inside an event block
    let parent = block.getParent();
    while (parent !== null) {
      if (parent.type === "event_run_clicked" || parent.type === "event_key_press") {
        return;
      }
      parent = parent.getParent();
    }

    // Only execute blocks that have the executable property set to true in their data
    if (!block.data || !block.data.executable) return;

    try {
      // Highlight the block being executed
      block.setHighlighted(true);

      // Initialize the JavaScript generator before generating code
      Blockly.JavaScript.init(workspace);

      // Generate code for just this block
      // @ts-ignore - blockToCode can accept our block type
      const code = /** @type {string} */ (Blockly.JavaScript.blockToCode(block));
      if (!code) {
        appendSimpleOutput(`Cannot execute this block: No code generator defined`, "warning");
        return;
      }

      appendDebugOutput(`Executing single block: ${block.type}`, "info");

      // Execute the code with the current workspace name
      // We don't want to clear event handlers when executing a single block
      const interpreter = this.#interpreterInitiator(code, (interpreter, globalObject) => {
        // Add the 'when' function for registering event handlers
        /**
         * @param {string} type - The event type (e.g., "RUN_CLICKED", "KEY_PRESS")
         * @param {{ node: any }} handler - The handler function
         * @param {any} [options] - Additional options (e.g., key for KEY_PRESS events)
         */
        const when = (type, handler, options) => {
          // Initialize the event handlers map for this type if it doesn't exist
          if (!this.#eventHandlers.has(type)) {
            this.#eventHandlers.set(type, []);
          }

          // Add the handler to the event handlers map
          this.#eventHandlers.get(type)?.push({ interpreter, fn: handler, data: options });
        };

        // Expose API functions to the interpreter
        interpreter.setProperty(globalObject, "when", interpreter.createNativeFunction(when));
        interpreter.setProperty(globalObject, "appendOutput", interpreter.createNativeFunction(Playground.#appendOutput));

        // Add a getCurrentEntityId function to the interpreter
        const getCurrentEntityIdFn = () => workspaceName ?? this.#activeWorkspaceName ?? "default";
        interpreter.setProperty(globalObject, "getCurrentEntityId", interpreter.createNativeFunction(getCurrentEntityIdFn));
      });

      // Run the interpreter
      await this.#runInterpreterToCompletion(interpreter, `single_block_${block.type}`);

      // Make sure the game is rendered with the new state
      this.#playgroundRenderer.render();
    } catch (error) {
      appendSimpleOutput(`Error executing block: ${error.message}`, "error");
    } finally {
      // Remove the highlight
      block.setHighlighted(false);
    }
  }

  /** @param {string} message */
  static #appendOutput(message) {
    appendSimpleOutput(message, "info");
  }

  /**
   * Highlight a block by ID
   * @param {string} blockId - The ID of the block to highlight
   */
  highlightBlock(blockId) {
    // Try to find the block in any workspace
    for(const workspace of this.#workspaces.values()) {
      const block = workspace.getBlockById(blockId);
      if (block) {
        // Use Blockly's built-in highlighting method
        block.setHighlighted(true);
        return;
      }
    }
  }

  /**
   * Clear all block highlights
   */
  clearBlockHighlights() {
    // Clear highlighting in all workspaces
    for(const workspace of this.#workspaces.values()) {
      // Clear all selections
      // @ts-ignore - Blockly.selected is a valid property but not in the type definitions
      if (Blockly.selected && Blockly.selected.workspace === workspace) {
        // @ts-ignore
        Blockly.selected.unselect();
      }

      // Clear any custom highlighting we might have added
      const blocks = workspace.getAllBlocks(false);
      for (const block of blocks) {
        if (block.setHighlighted) {
          block.setHighlighted(false);
        }
      }
    }
  }
};