/// <reference path="../@types/blocks.ts"/>

/** @param {Record<string, CustomBlockDefinition>} customBlocks */
export function defineCommonBlocks(customBlocks) {
  // Define a custom text block for output messages
  Blockly.Blocks['output_text'] = {
    init: function() {
      this.appendDummyInput()
          .appendField(new Blockly.FieldTextInput('Hello!'), 'TEXT');
      this.setOutput(true, 'String');
      this.setColour('#EEEEEE');
      this.setTooltip('Text string for output messages.');
      this.setHelpUrl('');
    }
  };

  Blockly.Blocks['math_on_list'] = {
    init: function() {
      this.appendValueInput("LIST")
          .setCheck("Array")
          .appendField(new Blockly.FieldDropdown([
            ["sum", "SUM"],
            ["min", "MIN"],
            ["max", "MAX"]
          ]), "OP")
          .appendField("of list");
      this.setOutput(true, "Number");
      this.setColour("#90B3D9");
      this.setTooltip("Performs a math operation on a list");
      this.setHelpUrl("");
    }
  };


  // Register the JavaScript generator for the output_text block
  Blockly.JavaScript.forBlock['output_text'] = function(block) {
    var text = block.getFieldValue('TEXT');
    return ['"' + text + '"', Blockly.JavaScript.ORDER_ATOMIC];
  };

  Blockly.defineBlocksWithJsonArray([
    {
      type: "event_run_clicked",
      message0: "when %1 clicked",
      args0: [
        {
          type: "field_icon",
          iconClass: "hgi hgi-stroke hgi-play"
        }
      ],
      message1: "%1",
      args1: [
        {
          type: "input_statement",
          name: "BODY"
        }
      ],
      colour: "#FFCC80", // Pastel Orange
      tooltip: "Run the blocks when the play button is clicked.",
      hat: "cap", // Visually shows it's the start
      inputsInline: false
    },
    {
      type: "event_key_press",
      message0: "when key %1 pressed",
      args0: [
        {
          type: "field_dropdown",
          name: "KEY",
          options: [
            ["space", "Space"],
            ["up arrow", "ArrowUp"],
            ["down arrow", "ArrowDown"],
            ["left arrow", "ArrowLeft"],
            ["right arrow", "ArrowRight"],
            ["enter", "Enter"]
          ]
        }
      ],
      message1: "%1",
      args1: [
        {
          type: "input_statement",
          name: "BODY"
        }
      ],
      colour: "#FFCC80", // Pastel Orange
      tooltip: "Run the blocks when the specified key is pressed.",
      hat: "cap", // Visually shows it's the start
      inputsInline: false
    },
    {
      type: "write_to_output",
      message0: "write to output %1",
      args0: [
        {
          type: "input_value",
          name: "MESSAGE"
        }
      ],
      previousStatement: true,
      nextStatement: true,
      colour: "#9E9E9E", // Medium gray color
      tooltip: "Write a message or variable value to the output console.",
      helpUrl: ""
    }
  ]);
  for(const key in customBlocks) {
    Blockly.Blocks[key] = customBlocks[key];
  }
}