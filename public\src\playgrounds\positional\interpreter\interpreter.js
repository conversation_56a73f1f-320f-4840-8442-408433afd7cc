// @ts-check
/// <reference path="../../../../src/core/global.d.ts"/>

import { appendDebugOutput, appendSimpleOutput } from "../../../utils/output.js";
import { initCommonInterpreterFunctions } from "../../../core/interpreter-common.js";

/**
 * Creates the API for the JavaScript interpreter to interact with the positional game
 * @param {import("../renderer/positional-game.js").default} game - The positional game instance
 * @param {string} [workspaceId] - The ID of the workspace this API is for
 * @returns {Object} - The API object with init method and methods property
 */
export default function createPlaygroundAPI(game, workspaceId) {
  // Store the workspace ID for this API instance
  const apiWorkspaceId = workspaceId || 'default';
  const methods = {
    /**
     * Move to a specific position
     * @param {number} x - The x coordinate
     * @param {number} y - The y coordinate
     */
    moveToPosition(x, y) {
      appendDebugOutput(`Moving ${apiWorkspaceId} to position (${x}, ${y})`, "info");
      appendSimpleOutput(`Moving ${apiWorkspaceId} to position (${x}, ${y})`, "info");
      game.moveToPosition(x, y, apiWorkspaceId);
    },

    /**
     * Move by a specific amount
     * @param {number} dx - The change in x
     * @param {number} dy - The change in y
     */
    moveBy(dx, dy) {
      appendDebugOutput(`Moving ${apiWorkspaceId} by (${dx}, ${dy})`, "info");
      appendSimpleOutput(`Moving ${apiWorkspaceId} by (${dx}, ${dy})`, "info");
      game.moveBy(dx, dy, apiWorkspaceId);
    },

    /**
     * Set the rotation of the object
     * @param {number} angle - The angle in degrees
     */
    setRotation(angle) {
      appendDebugOutput(`Setting ${apiWorkspaceId} rotation to ${angle} degrees`, "info");
      appendSimpleOutput(`Setting ${apiWorkspaceId} rotation to ${angle} degrees`, "info");
      game.setRotation(angle, apiWorkspaceId);
    },

    /**
     * Rotate by a specific amount
     * @param {number} angle - The angle in degrees
     */
    rotateBy(angle) {
      appendDebugOutput(`Rotating ${apiWorkspaceId} by ${angle} degrees`, "info");
      appendSimpleOutput(`Rotating ${apiWorkspaceId} by ${angle} degrees`, "info");
      game.rotateBy(angle, apiWorkspaceId);
    },

    /**
     * Get the current workspace name (entity ID)
     * @returns {string} The current workspace name
     */
    getCurrentEntityId() {
      return apiWorkspaceId;
    }
  };

  /**
   * Initialize the API for the interpreter
   * @param {InterpreterInstance} interpreter - The JavaScript interpreter
   * @param {object} globalObject - The global scope object
   */
  function init(interpreter, globalObject) {
    // Register all methods
    for (const methodName in methods) {
      if (methodName !== 'getCurrentEntityId') {
        interpreter.setProperty(
          globalObject,
          methodName,
          interpreter.createNativeFunction(methods[methodName])
        );
      }
    }

    // Set the getCurrentEntityId method
    interpreter.setProperty(
      globalObject,
      'getCurrentEntityId',
      interpreter.createNativeFunction(function() {
        return apiWorkspaceId;
      })
    );

    // Initialize common interpreter functions
    initCommonInterpreterFunctions(interpreter, globalObject);
  }

  return {
    init,
    methods
  };
}
