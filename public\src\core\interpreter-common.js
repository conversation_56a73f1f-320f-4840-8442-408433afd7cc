// @ts-check
/// <reference path="../core/global.d.ts"/>

/**
 * Initialize common interpreter functions that should be available in all playgrounds
 * @param {InterpreterInstance} interpreter - The JavaScript interpreter instance
 * @param {any} globalObject - The global scope object of the interpreter
 */
export function initCommonInterpreterFunctions(interpreter, globalObject) {
  // Function for generating random integers in a range (used by 'math_random_int' block)
  interpreter.setProperty(globalObject, "mathRandomInt",
    interpreter.createNativeFunction(function(/** @type {number} */ a, /** @type {number} */ b) {
      if (a > b) {
        // Swap a and b to ensure a is smaller
        const c = a;
        a = b;
        b = c;
      }
      return Math.floor(Math.random() * (b - a + 1) + a);
    })
  );

  // Helper function to ensure string comparisons work correctly
  interpreter.setProperty(globalObject, "compareStrings",
    interpreter.createNativeFunction(function(/** @type {string} */ a, /** @type {string} */ b, /** @type {string} */ op) {
      a = String(a);
      b = String(b);
      switch (op) {
        case '==': return a == b;
        case '!=': return a != b;
        case '<': return a < b;
        case '<=': return a <= b;
        case '>': return a > b;
        case '>=': return a >= b;
        default: return false;
      }
    })
  );

  // Function for sorting lists with custom compare function
  interpreter.setProperty(globalObject, "listsGetSortCompare",
    interpreter.createNativeFunction(
      /** @type {(type: string, direction: number) => (a: any, b: any) => number} */
      function(type, direction) {
        /** @type {Record<string, (a: any, b: any) => number>} */
        const compareFuncs = {
          "NUMERIC"(a, b) {
            return Number(a) - Number(b);
          },
          "TEXT"(a, b) {
            return String(a) > String(b) ? 1 : -1;
          },
          "IGNORE_CASE"(a, b) {
            return String(a).toLowerCase() > String(b).toLowerCase() ? 1 : -1;
          },
        };

        const compare = compareFuncs[type];

        return interpreter.createNativeFunction(function(a, b) {
          return compare(a, b) * direction;
        });
      }
    )
  );

  // Function for creating lists with repeated elements (used by 'lists_repeat' block)
  interpreter.setProperty(globalObject, "listsRepeat",
    interpreter.createNativeFunction(function(/** @type {any} */ value, /** @type {number} */ n) {
      const array = [];
      for (let i = 0; i < n; i++) {
        array[i] = value;
      }
      return array;
    })
  );

  // Function for getting random items from a list
  interpreter.setProperty(globalObject, "listsGetRandomItem",
    interpreter.createNativeFunction(function(/** @type {any[]} */ list, /** @type {boolean} */ remove) {
      const x = Math.floor(Math.random() * list.length);
      if (remove) {
        return list.splice(x, 1)[0];
      } else {
        return list[x];
      }
    })
  );
}
