// @ts-check

/**
 * @param {any[]} customTools
 * @returns {import("blockly").Toolbox["toolboxDef_"]}
*/
export function createToolbox(customTools = []) {
  return {
    kind: "categoryToolbox",
    contents: [
      {
        kind: "category",
        name: "Events",
        colour: "#FFCC80", // Pastel Orange
        cssConfig: {
          row: "blocklyTreeRow blocklyTreeRowEvents",
        },
        contents: [
          { kind: "BLOCK", type: "event_run_clicked" },
          { kind: "BLOCK", type: "event_key_press" }
        ]
      },
      ...customTools,

      {
        kind: "SEP",
      },

      {
        // Logic Category
        kind: "CATEGORY",
        name: "Logic",
        colour: "#D4C5F9", // Light purple/lavender
        cssConfig: {
          row: "blocklyTreeRow blocklyTreeRowLogic",
        },
        contents: [
          {
            kind: "BLOCK",
            type: "controls_if",
          },
          {
            kind: "BLOCK",
            type: "logic_compare",
          },
          {
            kind: "BLOCK",
            type: "logic_operation",
          },
          {
            kind: "BLOCK",
            type: "logic_negate",
          },
          {
            kind: "BLOCK",
            type: "logic_boolean",
          },
          {
            kind: "BLOCK",
            type: "logic_ternary",
          }
        ]
      },

      {
        // Loops Category
        kind: "CATEGORY",
        name: "Loops",
        colour: "#A5D6A7", // Light green
        cssConfig: {
          row: "blocklyTreeRow blocklyTreeRowLoops",
        },
        contents: [
          {
            kind: "BLOCK",
            type: "controls_repeat_ext",
            inputs: {
              TIMES: {
                shadow: {
                  type: "math_number",
                  fields: { NUM: 10 },
                },
              },
            },
          },
          {
            kind: "BLOCK",
            type: "controls_whileUntil",
          },
          {
            kind: "BLOCK",
            type: "controls_for",
            inputs: {
              FROM: {
                shadow: {
                  type: "math_number",
                  fields: { NUM: 1 },
                },
              },
              TO: {
                shadow: {
                  type: "math_number",
                  fields: { NUM: 10 },
                },
              },
              BY: {
                shadow: {
                  type: "math_number",
                  fields: { NUM: 1 },
                },
              },
            },
          },
          {
            kind: "BLOCK",
            type: "controls_forEach",
          },
          {
            kind: "BLOCK",
            type: "controls_flow_statements",
          },
        ],
      },

      {
        // Math Category
        kind: "CATEGORY",
        name: "Math",
        colour: "#90B3D9", // Lighter blue to match the pastel scheme
        cssConfig: {
          row: "blocklyTreeRow blocklyTreeRowMath",
        },
        contents: [
          {
            kind: "BLOCK",
            type: "math_number",
            fields: { NUM: 123 },
          },
          {
            kind: "BLOCK",
            type: "math_arithmetic",
            inputs: {
              A: {
                shadow: {
                  type: "math_number",
                  fields: { NUM: 1 },
                },
              },
              B: {
                shadow: {
                  type: "math_number",
                  fields: { NUM: 1 },
                },
              },
            },
          },
          {
            kind: "BLOCK",
            type: "math_single",
            inputs: {
              NUM: {
                shadow: {
                  type: "math_number",
                  fields: { NUM: 9 },
                },
              },
            },
          },
          {
            kind: "BLOCK",
            type: "math_trig",
            inputs: {
              NUM: {
                shadow: {
                  type: "math_number",
                  fields: { NUM: 45 },
                },
              },
            },
          },
          {
            kind: "BLOCK",
            type: "math_atan2",
            inputs: {
              X: {
                shadow: {
                  type: "math_number",
                  fields: { NUM: 1 },
                },
              },
              Y: {
                shadow: {
                  type: "math_number",
                  fields: { NUM: 1 },
                },
              },
            },
          },
          {
            kind: "BLOCK",
            type: "math_constant",
          },
          {
            kind: "BLOCK",
            type: "math_number_property",
            inputs: {
              NUMBER_TO_CHECK: {
                shadow: {
                  type: "math_number",
                  fields: { NUM: 0 },
                },
              },
            },
          },
          {
            kind: "BLOCK",
            type: "math_round",
            inputs: {
              NUM: {
                shadow: {
                  type: "math_number",
                  fields: { NUM: 3.1 },
                },
              },
            },
          },
          {
            kind: "BLOCK",
            type: "math_on_list",
            fields: {
              OP: "SUM"
            }
          },
          {
            kind: "BLOCK",
            type: "math_modulo",
            inputs: {
              DIVIDEND: {
                shadow: {
                  type: "math_number",
                  fields: { NUM: 64 },
                },
              },
              DIVISOR: {
                shadow: {
                  type: "math_number",
                  fields: { NUM: 10 },
                },
              },
            },
          },
          {
            kind: "BLOCK",
            type: "math_constrain",
            inputs: {
              VALUE: {
                shadow: {
                  type: "math_number",
                  fields: { NUM: 50 },
                },
              },
              LOW: {
                shadow: {
                  type: "math_number",
                  fields: { NUM: 1 },
                },
              },
              HIGH: {
                shadow: {
                  type: "math_number",
                  fields: { NUM: 100 },
                },
              },
            },
          },
          {
            kind: "BLOCK",
            type: "math_random_int",
            inputs: {
              FROM: {
                shadow: {
                  type: "math_number",
                  fields: { NUM: 1 },
                },
              },
              TO: {
                shadow: {
                  type: "math_number",
                  fields: { NUM: 100 },
                },
              },
            },
          },
          {
            kind: "BLOCK",
            type: "math_random_float",
          },
        ],
      },

      {
        kind: "SEP",
      },

      {
        // Variables Category
        kind: "CATEGORY",
        custom: "VARIABLE",
        name: "Variables",
        colour: "#F48FB1", // Light pink/salmon
        cssConfig: {
          row: "blocklyTreeRow blocklyTreeRowVariables",
        },
      },

      {
        // Lists Category
        kind: "CATEGORY",
        name: "Lists",
        colour: "#ffb4a2",  // Updated to coral-orange
        cssConfig: {
          row: "blocklyTreeRow blocklyTreeRowLists",
        },
        contents: [
          {
            kind: "BLOCK",
            type: "lists_create_empty",
          },
          {
            kind: "BLOCK",
            type: "lists_create_with",
          },
          {
            kind: "BLOCK",
            type: "lists_repeat",
            inputs: {
              NUM: {
                shadow: {
                  type: "math_number",
                  fields: { NUM: 5 },
                },
              },
            },
          },
          {
            kind: "BLOCK",
            type: "lists_getIndex",
            // Preconfigure for "get at"
            fields: {
              MODE: "GET",
              WHERE: "FROM_START"
            },
            inputs: {
              AT: {
                shadow: {
                  type: "math_number",
                  fields: { NUM: 1 }
                }
              }
            }
          },
          {
            kind: "BLOCK",
            type: "lists_getIndex",
            // Preconfigure for "get and remove at"
            fields: {
              MODE: "GET_REMOVE",
              WHERE: "FROM_START"
            },
            inputs: {
              AT: {
                shadow: {
                  type: "math_number",
                  fields: { NUM: 1 }
                }
              }
            }
          },
          {
            kind: "BLOCK",
            type: "lists_getIndex",
            // Preconfigure for "get random"
            fields: {
              MODE: "GET",
              WHERE: "RANDOM"
            }
          },
          {
            kind: "BLOCK",
            type: "lists_setIndex",
            // Preconfigure for "insert at"
            fields: {
              MODE: "INSERT",
              WHERE: "FROM_START"
            },
            inputs: {
              AT: {
                shadow: {
                  type: "math_number",
                  fields: { NUM: 1 }
                }
              }
            }
          },
          {
            kind: "BLOCK",
            type: "lists_setIndex",
            // Preconfigure for "remove at"
            fields: {
              MODE: "REMOVE",
              WHERE: "FROM_START"
            },
            inputs: {
              AT: {
                shadow: {
                  type: "math_number",
                  fields: { NUM: 1 }
                }
              }
            }
          },
          {
            kind: "BLOCK",
            type: "lists_length",
          },
          {
            kind: "BLOCK",
            type: "lists_isEmpty",
          },
          {
            kind: "BLOCK",
            type: "lists_indexOf",
          },
          {
            kind: "BLOCK",
            type: "lists_getSublist",
          },
          {
            kind: "BLOCK",
            type: "lists_sort",
          },
        ],
      },

      {
        kind: "SEP",
      },

      {
        kind: "category",
        name: "Output",
        colour: "#9E9E9E", // Medium gray color
        cssConfig: {
          row: "blocklyTreeRow blocklyTreeRowOutput",
          container: "blocklyTreeRowOutput-container"
        },
        contents: [
          { kind: "BLOCK", type: "write_to_output", inputs: { MESSAGE: { shadow: { type: "output_text", fields: { TEXT: "Hello!" } } } } },
          { kind: "BLOCK", type: "write_to_output" }
        ]
      }

    ]
  };
}
