/** @typedef {{ stroke?: string | CanvasGradient | CanvasPattern }} Renderer2DBaseStyle */
/**
 * @exports
 * @typedef {CanvasRenderingContext2DSettings & { background?: string | CanvasGradient | CanvasPattern }} Renderer2DConfig
*/

// @ts-check

export default class Renderer2D extends HTMLCanvasElement {
  /** @type {CanvasRenderingContext2D} */
  #context;
  #background;

  /** @param {Renderer2DConfig} [config] */
  constructor(config = {}) {
    super();
    this.#background = config.background ?? this.getAttribute("background");
    /** @type {CanvasRenderingContext2DSettings} */
    const options = {
      alpha: config?.alpha ?? this.#getSettingAttribute("alpha"),
      desynchronized: config?.desynchronized ?? this.#getSettingAttribute("desynchronized"),
      willReadFrequently: config?.willReadFrequently ?? this.#getSettingAttribute("willReadFrequently"),
      colorSpace: config?.colorSpace ?? this.#getSettingAttribute("colorSpace")
    };
    const context = this.getContext("2d", options);
    if(context === null) {
      throw new Error("Unable to get Renderer2D context");
    }
    this.#context = context;
  }

  static get observedAttributes() {
    return /** @type {const} */ ([ "background" ]);
  }

  get context() {
    return this.#context;
  }

  get background() {
    return this.#background;
  }
  set background(value) {
    this.#background = value;
  }

  /**
   * @template {keyof CanvasRenderingContext2DSettings} K
   * @param {K} name
   * @returns {CanvasRenderingContext2DSettings[K]}
   */
  #getSettingAttribute(name) {
    /** @type {unknown} */
    let returnValue;
    switch(name) {
      case "alpha":
      case "desynchronized":
      case "willReadFrequently":
      {
        const value = this.getAttribute(name);
        if(value !== null) {
          returnValue = value !== "false";
        }
      }
      break;
      case "colorSpace":
        returnValue = this.getAttribute(name);
    }
    return /** @type {CanvasRenderingContext2DSettings[K]} */ (returnValue ?? undefined);
  }

  /**
   * @template {typeof Renderer2D["observedAttributes"][number]} K
   * @param {K} name
   * @param {string | null} oldValue
   * @param {string} newValue
  */
  attributeChangedCallback(name, oldValue, newValue) {
    if(name === "background" && newValue !== oldValue) {
      this.#background = newValue;
    }
  }


  clear() {
    if(this.#background) {
      this.#context.fillStyle = this.#background;
      this.#context.fillRect(0, 0, this.#context.canvas.width, this.#context.canvas.height);
      return;
    }
    this.#context.clearRect(0, 0, this.#context.canvas.width, this.#context.canvas.height);
  }

  /** @param {Renderer2DBaseStyle} [style] */
  #applyBaseStyle(style) {
    if(style?.stroke) {
      this.#context.strokeStyle = style.stroke;
    }
  }

  /**
   * @param {number} x1
   * @param {number} x2
   * @param {number} y1
   * @param {number} y2
   * @param {Renderer2DBaseStyle & { width?: number; cap?: CanvasLineCap; dash?: number[]; dashOffset?: number; join?: CanvasLineJoin }} [style]
  */
  openLine(x1, y1, x2, y2, style) {
    this.#applyBaseStyle(style);
    if(style?.width) {
      this.#context.lineWidth = style.width;
    }
    if(style?.cap) {
      this.#context.lineCap = style?.cap;
    }
    if(style?.dash) {
      this.#context.setLineDash(style?.dash);
    }
    if(style?.dashOffset) {
      this.#context.lineDashOffset = style?.dashOffset;
    }
    if(style?.join) {
      this.#context.lineJoin = style?.join;
    }
    this.#context.moveTo(x1, y1);
    this.#context.lineTo(x2, y2);
  }

  /** @type {Renderer2D["openLine"]} */
  line(...args) {
    this.#context.save();
    this.#context.beginPath();
    this.openLine(...args);
    this.#context.stroke();
    this.#context.closePath();
    this.#context.restore();
  }

  /**
   * @param {number} x
   * @param {number} y
   * @param {number} width
   * @param {number} height
   * @param {Renderer2DBaseStyle & { fill?: string | CanvasGradient | CanvasPattern; fillRule?: CanvasFillRule }} [style]
  */
  rect(x, y, width, height, style) {
    this.#context.save();
    this.#context.beginPath();
    this.#applyBaseStyle(style);
    if(style?.fill) {
      this.#context.fillStyle = style.fill;
    }
    this.#context.rect(x, y, width, height);
    if(style?.stroke) {
      this.#context.stroke();
    }
    if(style?.fill) {
      this.#context.fill(style.fillRule);
    }
    this.#context.closePath();
    this.#context.restore();
  }

  /**
   * @param {number} x
   * @param {number} y
   * @param {number} rx
   * @param {number} ry
   * @param {number} rotation
   * @param {number} angleStart
   * @param {number} angleEnd
   * @param {boolean} counterClockwise
   * @param {Renderer2DBaseStyle & { fill?: string | CanvasGradient | CanvasPattern; fillRule?: CanvasFillRule }} style
  */
  ellipse(x, y, rx, ry = rx, rotation = 0, angleStart = 0, angleEnd = Math.PI * 2, counterClockwise = false, style = {}) {
    this.#applyBaseStyle(style);
    if(style?.fill) {
      this.#context.fillStyle = style.fill;
    }
    this.#context.ellipse(x, y, rx, ry, rotation, angleStart, angleEnd, counterClockwise);
    if(style?.stroke) {
      this.#context.stroke();
    }
    if(style?.fill) {
      this.#context.fill(style.fillRule);
    }
  }

  /**
   * @param {CanvasImageSource} image
   * @param {[ x: number, y: number, width: number, height: number ]} source
   * @param {[ x: number, y: number, width: number, height: number ]} destination
  */
  image(image, source, destination, angle = 0) {
    const x = destination[0] + destination[2] / 2;
    const y = destination[1] + destination[3] / 2;
    this.#context.save();
    this.#context.translate(x, y);
    this.#context.rotate(angle);
    this.#context.drawImage(image, ...source, -destination[2] / 2, -destination[3] / 2, destination[2], destination[3]);
    this.#context.restore();
  }
  /**
   * @param {CanvasImageSource} image
   * @param {number} dw
   * @param {number} dh
  */
  createPattern(image, dw, dh) {
    const offscreenCanvasContext = Renderer2D.#createOffscreenCanvas(dw, dh);
    // const resizedImage = this.#context.getImageData(0, 0, dw, dh);
    // this.#context.createPattern(resizedImage);
  }

  /**
   * @param {number} width
   * @param {number} height
  */
  static #createOffscreenCanvas(width, height) {
    const canvas = new OffscreenCanvas(width, height);
    const context = canvas.getContext("2d");
    if(context === null) {
      return;
    }
    context;
  }

  /** @type {Readonly<[ tagName: string, constructor: typeof HTMLElement, options: ElementDefinitionOptions ]>} */
  static definition = Object.freeze([ "renderer-2d", this, { extends: "canvas" } ]);

}
